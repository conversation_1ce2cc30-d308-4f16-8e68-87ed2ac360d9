/**
 * Utility functions for consistent pagination handling across the application
 */

export interface PaginationParams {
  page?: number;
  limit?: number;
  skip?: number;
  take?: number;
}

export interface PaginationResult {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationOptions {
  skip: number;
  take: number;
}

/**
 * Parse and validate pagination parameters from request query
 * Supports both page/limit and skip/take formats
 */
export function parsePaginationParams(query: any): { 
  paginationOptions: PaginationOptions; 
  paginationMeta: { page: number; limit: number } 
} {
  let page: number;
  let limit: number;

  // Check if using page/limit format
  if (query.page !== undefined || query.limit !== undefined) {
    page = query.page ? parseInt(query.page as string) : 1;
    limit = query.limit ? parseInt(query.limit as string) : 10;
  } 
  // Check if using skip/take format (convert to page/limit)
  else if (query.skip !== undefined || query.take !== undefined) {
    const skip = query.skip ? parseInt(query.skip as string) : 0;
    const take = query.take ? parseInt(query.take as string) : 10;

    limit = take;
    page = Math.floor(skip / take) + 1; // Convert 0-based skip to 1-based page
  }
  // Default values
  else {
    page = 1;
    limit = 10;
  }

  // Validate pagination parameters
  if (page < 1) {
    throw new Error('Page number must be 1 or greater');
  }
  
  if (limit < 1 || limit > 100) {
    throw new Error('Limit must be between 1 and 100');
  }

  const skip = (page - 1) * limit; // Convert 1-based page to 0-based skip
  const take = limit;

  return {
    paginationOptions: { skip, take },
    paginationMeta: { page, limit }
  };
}

/**
 * Create consistent pagination result object
 */
export function createPaginationResult(
  page: number,
  limit: number,
  total: number
): PaginationResult {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page * limit < total;
  const hasPrev = page > 1;

  return {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev
  };
}

/**
 * Create standardized paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  additionalData?: Record<string, any>
) {
  return {
    success: true,
    data: {
      ...additionalData,
      pagination: createPaginationResult(page, limit, total)
    }
  };
}
