import { PrismaClient } from '@prisma/client';

export enum AdminActionType {
  USER_ACTIVATION = 'USER_ACTIVATION',
  USER_DEACTIVATION = 'USER_DEACTIVATION',
  OPERATOR_APPROVAL = 'OPERATOR_APPROVAL',
  OPERATOR_REJECTION = 'OPERATOR_REJECTION'
}

export interface AdminActionData {
  adminId: string;
  targetUserId: string;
  actionType: AdminActionType;
  reason: string;
  metadata?: any;
}

export interface AdminActionRecord {
  id: string;
  admin_id: string;
  target_user_id: string;
  action_type: AdminActionType;
  reason: string;
  metadata?: any;
  created_at: Date;
  updated_at: Date;
}

export class AdminActionService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Record an admin action with reason
   */
  async recordAdminAction(data: AdminActionData): Promise<AdminActionRecord> {
    try {
      // For now, we'll store this in the audit_logs table with a special category
      // until the migration is applied
      const auditLog = await this.prisma.auditLog.create({
        data: {
          user_id: data.targetUserId,
          admin_id: data.adminId,
          action: data.actionType,
          details: JSON.stringify({
            reason: data.reason,
            metadata: data.metadata,
            action_type: data.actionType,
            ip_address: '127.0.0.1', // Store in details since not in schema
            user_agent: 'Admin Panel', // Store in details since not in schema
          }),
        }
      });

      // Return in the expected format
      return {
        id: auditLog.id,
        admin_id: data.adminId,
        target_user_id: data.targetUserId,
        action_type: data.actionType,
        reason: data.reason,
        metadata: data.metadata,
        created_at: auditLog.created_at,
        updated_at: auditLog.created_at
      };
    } catch (error) {
      console.error('Error recording admin action:', error);
      throw new Error('Failed to record admin action');
    }
  }

  /**
   * Get admin actions for a specific user
   */
  async getUserAdminActions(userId: string): Promise<AdminActionRecord[]> {
    try {
      // Query from audit_logs for now
      const auditLogs = await this.prisma.auditLog.findMany({
        where: {
          user_id: userId,
          action: {
            in: [
              AdminActionType.USER_ACTIVATION,
              AdminActionType.USER_DEACTIVATION,
              AdminActionType.OPERATOR_APPROVAL,
              AdminActionType.OPERATOR_REJECTION
            ]
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        include: {
          admin: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      return auditLogs
        .filter(log => log.user_id && log.admin_id)
        .map(log => {
          const details = JSON.parse(typeof log.details === 'string' ? log.details : '{}');
          return {
            id: log.id,
            admin_id: log.admin_id!,
            target_user_id: log.user_id!,
            action_type: log.action as AdminActionType,
            reason: details.reason || 'No reason provided',
            metadata: details.metadata,
            created_at: log.created_at,
            updated_at: log.created_at
          };
        });
    } catch (error) {
      console.error('Error fetching user admin actions:', error);
      throw new Error('Failed to fetch admin actions');
    }
  }

  /**
   * Get admin actions performed by a specific admin
   */
  async getAdminActions(adminId: string, limit: number = 50): Promise<AdminActionRecord[]> {
    try {
      const auditLogs = await this.prisma.auditLog.findMany({
        where: {
          admin_id: adminId,
          action: {
            in: [
              AdminActionType.USER_ACTIVATION,
              AdminActionType.USER_DEACTIVATION,
              AdminActionType.OPERATOR_APPROVAL,
              AdminActionType.OPERATOR_REJECTION
            ]
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              user_type: true
            }
          }
        }
      });

      return auditLogs
        .filter(log => log.user_id && log.admin_id)
        .map(log => {
          const details = JSON.parse(typeof log.details === 'string' ? log.details : '{}');
          return {
            id: log.id,
            admin_id: log.admin_id!,
            target_user_id: log.user_id!,
            action_type: log.action as AdminActionType,
            reason: details.reason || 'No reason provided',
            metadata: details.metadata,
            created_at: log.created_at,
            updated_at: log.created_at
          };
        });
    } catch (error) {
      console.error('Error fetching admin actions:', error);
      throw new Error('Failed to fetch admin actions');
    }
  }

  /**
   * Get the latest admin action for a user
   */
  async getLatestAdminAction(userId: string): Promise<AdminActionRecord | null> {
    try {
      const actions = await this.getUserAdminActions(userId);
      return actions.length > 0 ? actions[0] : null;
    } catch (error) {
      console.error('Error fetching latest admin action:', error);
      return null;
    }
  }
}
