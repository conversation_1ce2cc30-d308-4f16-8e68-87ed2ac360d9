/* eslint-disable no-console */
import { PrismaClient } from "@prisma/client";

import {
  AdminNotification,
  NotificationType,
  NotificationPriority,
} from "../types/models";

export interface CreateAdminNotificationData {
  adminId: string;
  shipmentId?: string;
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

export interface AdminNotificationFilters {
  read?: boolean;
  type?: NotificationType;
  priority?: NotificationPriority;
  shipmentId?: string;
  fromDate?: Date;
  toDate?: Date;
  search?: string;
}

export interface PaginationOptions {
  skip?: number;
  take?: number;
}

export class AdminNotificationService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Create a single admin notification
   */
  async createAdminNotification(
    data: CreateAdminNotificationData
  ): Promise<AdminNotification> {
    try {
      console.log(`📧 Creating admin notification for admin ${data.adminId}:`, {
        type: data.type,
        title: data.title,
        shipmentId: data.shipmentId,
      });

      const notification = await this.prisma.adminNotification.create({
        data: {
          admin_id: data.adminId,
          shipment_id: data.shipmentId || null,
          notification_type: data.type,
          title: data.title,
          message: data.message,
          priority: data.priority || NotificationPriority.NORMAL,
          metadata: data.metadata || undefined,
          expires_at: data.expiresAt || null,
        },
      });

      console.log(`✅ Created admin notification ${notification.id}`);
      return notification as AdminNotification;
    } catch (error) {
      console.error("❌ Failed to create admin notification:", error);
      throw error;
    }
  }

  /**
   * Create multiple admin notifications for different admins
   */
  async createBulkAdminNotifications(
    notifications: CreateAdminNotificationData[]
  ): Promise<AdminNotification[]> {
    try {
      console.log(`📧 Creating ${notifications.length} bulk admin notifications`);

      const createdNotifications = await this.prisma.$transaction(
        notifications.map((data) =>
          this.prisma.adminNotification.create({
            data: {
              admin_id: data.adminId,
              shipment_id: data.shipmentId || null,
              notification_type: data.type,
              title: data.title,
              message: data.message,
              priority: data.priority || NotificationPriority.NORMAL,
              metadata: data.metadata || undefined,
              expires_at: data.expiresAt || undefined,
            },
          })
        )
      );

      console.log(
        `✅ Created ${createdNotifications.length} bulk admin notifications`
      );
      return createdNotifications as AdminNotification[];
    } catch (error) {
      console.error("❌ Failed to create bulk admin notifications:", error);
      throw error;
    }
  }

  /**
   * Get admin notifications by admin ID with filtering and pagination
   */
  async getAdminNotificationsByAdminId(
    adminId: string,
    filters: AdminNotificationFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<AdminNotification[]> {
    try {
      const where: any = { admin_id: adminId };

      // Apply filters
      if (filters.read !== undefined) {
        where.read = filters.read;
      }

      if (filters.type) {
        where.notification_type = filters.type;
      }

      if (filters.priority) {
        where.priority = filters.priority;
      }

      if (filters.shipmentId) {
        where.shipment_id = filters.shipmentId;
      }

      if (filters.fromDate || filters.toDate) {
        where.created_at = {};
        if (filters.fromDate) {
          where.created_at.gte = filters.fromDate;
        }
        if (filters.toDate) {
          where.created_at.lte = filters.toDate;
        }
      }

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: "insensitive" } },
          { message: { contains: filters.search, mode: "insensitive" } },
        ];
      }

      const notifications = await this.prisma.adminNotification.findMany({
        where,
        orderBy: { created_at: "desc" },
        skip: pagination.skip,
        take: pagination.take,
        include: {
          shipment: {
            select: {
              id: true,
              tracking_code: true,
              status: true,
            },
          },
        },
      });

      return notifications as AdminNotification[];
    } catch (error) {
      console.error("❌ Failed to get admin notifications:", error);
      throw error;
    }
  }

  /**
   * Get admin notification count with filters
   */
  async getAdminNotificationCount(
    adminId: string,
    filters: AdminNotificationFilters = {}
  ): Promise<number> {
    try {
      const where: any = { admin_id: adminId };

      // Apply same filters as getAdminNotificationsByAdminId
      if (filters.read !== undefined) {
        where.read = filters.read;
      }

      if (filters.type) {
        where.notification_type = filters.type;
      }

      if (filters.priority) {
        where.priority = filters.priority;
      }

      if (filters.shipmentId) {
        where.shipment_id = filters.shipmentId;
      }

      if (filters.fromDate || filters.toDate) {
        where.created_at = {};
        if (filters.fromDate) {
          where.created_at.gte = filters.fromDate;
        }
        if (filters.toDate) {
          where.created_at.lte = filters.toDate;
        }
      }

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: "insensitive" } },
          { message: { contains: filters.search, mode: "insensitive" } },
        ];
      }

      return await this.prisma.adminNotification.count({ where });
    } catch (error) {
      console.error("❌ Failed to get admin notification count:", error);
      throw error;
    }
  }

  /**
   * Mark admin notification as read
   */
  async markAdminNotificationAsRead(
    notificationId: string,
    adminId: string
  ): Promise<AdminNotification> {
    try {
      const notification = await this.prisma.adminNotification.update({
        where: {
          id: notificationId,
          admin_id: adminId, // Ensure admin can only mark their own notifications
        },
        data: {
          read: true,
          read_at: new Date(),
        },
      });

      console.log(`✅ Marked admin notification ${notificationId} as read`);
      return notification as AdminNotification;
    } catch (error) {
      console.error("❌ Failed to mark admin notification as read:", error);
      throw error;
    }
  }

  /**
   * Mark all admin notifications as read for an admin
   */
  async markAllAdminNotificationsAsRead(adminId: string): Promise<number> {
    try {
      const result = await this.prisma.adminNotification.updateMany({
        where: {
          admin_id: adminId,
          read: false,
        },
        data: {
          read: true,
          read_at: new Date(),
        },
      });

      console.log(`✅ Marked ${result.count} admin notifications as read for admin ${adminId}`);
      return result.count;
    } catch (error) {
      console.error("❌ Failed to mark all admin notifications as read:", error);
      throw error;
    }
  }

  /**
   * Delete admin notification
   */
  async deleteAdminNotification(
    notificationId: string,
    adminId: string
  ): Promise<void> {
    try {
      await this.prisma.adminNotification.delete({
        where: {
          id: notificationId,
          admin_id: adminId, // Ensure admin can only delete their own notifications
        },
      });

      console.log(`✅ Deleted admin notification ${notificationId}`);
    } catch (error) {
      console.error("❌ Failed to delete admin notification:", error);
      throw error;
    }
  }

  /**
   * Notify all active admins about a system event
   */
  async notifyAllAdmins(
    type: NotificationType,
    title: string,
    message: string,
    priority: NotificationPriority = NotificationPriority.NORMAL,
    metadata?: Record<string, any>,
    shipmentId?: string
  ): Promise<AdminNotification[]> {
    try {
      // Get all active admins
      const activeAdmins = await this.prisma.admin.findMany({
        where: { status: "ACTIVE" },
        select: { id: true },
      });

      if (activeAdmins.length === 0) {
        console.log("⚠️ No active admins found to notify");
        return [];
      }

      // Create notifications for all active admins
      const notifications = await this.createBulkAdminNotifications(
        activeAdmins.map((admin) => ({
          adminId: admin.id,
          type,
          title,
          message,
          priority,
          metadata,
          shipmentId,
        }))
      );

      console.log(`✅ Notified ${activeAdmins.length} admins about: ${title}`);
      return notifications;
    } catch (error) {
      console.error("❌ Failed to notify all admins:", error);
      throw error;
    }
  }
}
