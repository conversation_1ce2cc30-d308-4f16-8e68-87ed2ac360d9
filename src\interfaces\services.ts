/* eslint-disable no-unused-vars */
import {
  User,
  AccessOperator,
  CarOperator,
  Shipment,
  ShipmentQRLabel,
  Notification,
  NotificationPreference,
  AuditLog,
  GeoPoint,
  ShipmentStatus,
} from "../types/models";

// User Service Interface
export interface IUserService {
  createUser(
    userData: Omit<User, "id" | "created_at" | "updated_at">,
    accessOperatorData?: any,
    carOperatorData?: any
  ): Promise<User>;
  getUserById(id: string): Promise<User | null>;
  getUserByEmail(email: string): Promise<User | null>;
  updateUser(id: string, userData: Partial<User>): Promise<User>;
  changeUserStatus(id: string, status: string): Promise<User>;
  validateCredentials(email: string, password: string): Promise<User | null>;
  verifyEmail(email: string, token: string): Promise<User>;
  sendPasswordResetEmail(email: string): Promise<void>;
  resetPassword(
    email: string,
    token: string,
    newPassword: string
  ): Promise<void>;
  changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void>;
  getAllUsers(options?: {
    skip?: number;
    take?: number;
    search?: string;
    status?: string;
    user_type?: string;
  }): Promise<{ users: User[]; total: number }>;
  logout(userId: string): Promise<void>;
}

// Access Operator Service Interface
export interface IAccessOperatorService {
  createAccessOperator(
    userData: Omit<User, "id" | "created_at" | "updated_at">,
    aoData: Omit<AccessOperator, "id" | "created_at">
  ): Promise<AccessOperator>;
  getAccessOperatorById(id: string, adminMode?: boolean): Promise<AccessOperator | null>;
  updateAccessOperator(
    id: string,
    aoData: Partial<AccessOperator>
  ): Promise<AccessOperator>;
  approveAccessOperator(id: string): Promise<AccessOperator>;
  getNearbyAccessOperators(
    location: GeoPoint,
    radiusKm: number
  ): Promise<AccessOperator[]>;
}

// Car Operator Service Interface
export interface ICarOperatorService {
  createCarOperator(
    userData: Omit<User, "id" | "created_at" | "updated_at">,
    coData: Omit<CarOperator, "id" | "created_at">
  ): Promise<CarOperator>;
  getCarOperatorById(id: string): Promise<CarOperator | null>;
  updateCarOperator(
    id: string,
    coData: Partial<CarOperator>
  ): Promise<CarOperator>;
  approveCarOperator(id: string): Promise<CarOperator>;
  getAvailableCarOperators(
    originAoId: string,
    destAoId: string
  ): Promise<CarOperator[]>;
}

// Shipment Service Interface
export interface IShipmentService {
  createShipment(
    shipmentData: Omit<Shipment, "id" | "created_at" | "updated_at" | "status">
  ): Promise<Shipment>;
  getShipmentById(id: string): Promise<Shipment | null>;
  updateShipment(
    id: string,
    shipmentData: Partial<Shipment>
  ): Promise<Shipment>;
  updateShipmentStatus(id: string, status: ShipmentStatus): Promise<Shipment>;
  getShipmentsByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number; stats?: any }>;
  getShipmentsByAccessOperatorId(
    aoId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number; stats?: any }>;
  getPendingShipmentsByCustomerId(
    customerId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ shipments: Shipment[]; total: number }>;
  getPendingShipmentsByAccessOperatorId(
    aoId: string,
    options?: { skip?: number; take?: number; role?: string }
  ): Promise<{ shipments: Shipment[]; total: number }>;
  getPendingShipmentsForCarOperator(
    carOperatorId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ shipments: Shipment[]; total: number }>;
  getShipmentsByCarOperator(
    carOperatorId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number }>;
  getShipmentByQRCode(qrCode: string): Promise<Shipment | null>;
  validatePickupCode(shipmentId: string, pickupCode: string): Promise<boolean>;
}

// QR Label Service Interface
export interface IQRLabelService {
  generateQRLabels(aoId: string, count: number): Promise<ShipmentQRLabel[]>;
  getQRLabelById(id: string): Promise<ShipmentQRLabel | null>;
  getQRLabelByValue(qrValue: string): Promise<ShipmentQRLabel | null>;
  assignQRLabelToShipment(
    qrLabelId: string,
    shipmentId: string
  ): Promise<ShipmentQRLabel>;
  markQRLabelAsUsed(id: string): Promise<ShipmentQRLabel>;
  getUnusedQRLabels(
    aoId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ qrLabels: ShipmentQRLabel[]; total: number }>;
}

// Notification Service Interface
export interface INotificationService {
  createNotification(
    userId: string,
    shipmentId: string,
    type: string,
    message: string
  ): Promise<Notification>;
  /**
   * Create a notification with advanced options (preferred method)
   */
  createNotificationEnhanced?(data: any): Promise<Notification>;
  /**
   * Create multiple notifications in one transaction
   */
  createBulkNotifications?(notifications: any[]): Promise<Notification[]>;
  /**
   * Get notifications for a user with optional filtering & pagination
   */
  getNotificationsByUserId(
    userId: string,
    filters?: any,
    pagination?: any
  ): Promise<Notification[]>;
  getUnreadNotificationsByUserId(userId: string): Promise<Notification[]>;
  /**
   * Get total notification count for a user (optionally filtered)
   */
  getNotificationCount?(userId: string, filters?: any): Promise<number>;
  markNotificationAsRead(id: string): Promise<Notification>;
  markAllNotificationsAsRead(userId: string): Promise<void>;
  getNotificationPreferences(userId: string): Promise<NotificationPreference>;
  updateNotificationPreferences(
    userId: string,
    preferences: Partial<
      Omit<
        NotificationPreference,
        "id" | "user_id" | "created_at" | "updated_at"
      >
    >
  ): Promise<NotificationPreference>;
}

// Audit Log Service Interface
export interface IAuditLogService {
  createAuditLog(
    userId: string | undefined,
    action: string,
    shipmentId?: string,
    details?: Record<string, any>,
    adminId?: string
  ): Promise<AuditLog>;
  getAuditLogsByShipmentId(shipmentId: string): Promise<AuditLog[]>;
  getAuditLogsByUserId(userId: string): Promise<AuditLog[]>;
}
