generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String                  @id @default(uuid()) @db.Uuid
  name                  String
  email                 String                  @unique
  password_hash         String
  phone                 String
  user_type             UserType
  status                UserStatus              @default(PENDING)
  email_verified        Boolean                 @default(false)
  created_at            DateTime                @default(now())
  updated_at            DateTime                @updatedAt
  updated_by            String?                 @db.Uuid
  accessOperator        AccessOperator?
  auditLogs             AuditLog[]
  carOperator           CarOperator?
  emailVerifications    EmailVerification[]
  userVerifications     UserVerification[]
  loginAttempts         LoginAttempt[]
  ManagementAction      ManagementAction[]
  passwordResets        PasswordReset[]
  securityEvents        SecurityEvent[]
  shipmentStatusChanges ShipmentStatusHistory[]
  shipments             Shipment[]              @relation("CustomerShipments")
  assignedShipments     Shipment[]              @relation("AssignedCarOperator")
  updatedByUser         User?                   @relation("UserUpdatedBy", fields: [updated_by], references: [id])
  updatedUsers          User[]                  @relation("UserUpdatedBy")
  updatedShipments      Shipment[]              @relation("ShipmentUpdatedBy")
  updatedAccessOperators AccessOperator[]       @relation("AccessOperatorUpdatedBy")
  updatedCarOperators   CarOperator[]           @relation("CarOperatorUpdatedBy")
  updatedQRLabels       ShipmentQRLabel[]       @relation("QRLabelUpdatedBy")
  updatedSystemSettings SystemSettings[]        @relation("SystemSettingsUpdatedBy")
  shipmentPhotos        ShipmentPhoto[]         @relation("ShipmentPhotos")
  notifications         Notification[]          @relation("UserNotifications")
  notificationPreferences NotificationPreference? @relation("UserNotificationPreferences")
  adminActionsReceived  AdminAction[]           @relation("UserAdminActions")

  @@map("users")
}

model Admin {
  id                String              @id @default(uuid()) @db.Uuid
  email             String              @unique
  password_hash     String
  name              String
  role              AdminRole           @default(ADMIN)
  status            UserStatus          @default(ACTIVE)
  email_verified    Boolean             @default(false)
  created_at        DateTime            @default(now())
  updated_at        DateTime            @updatedAt
  updated_by        String?             @db.Uuid
  verifications     AdminVerification[]
  auditLogs         AuditLog[]
  loginAttempts     LoginAttempt[]
  managementActions ManagementAction[]
  securityEvents    SecurityEvent[]
  notifications     AdminNotification[] @relation("AdminNotifications")
  updatedByAdmin    Admin?              @relation("AdminUpdatedBy", fields: [updated_by], references: [id])
  updatedAdmins     Admin[]             @relation("AdminUpdatedBy")
  adminActions      AdminAction[]       @relation("AdminActions")

  @@map("admins")
}

model AccessOperator {
  id                        String               @id @db.Uuid
  business_name             String
  address                   String
  geo_latitude              Float?
  geo_longitude             Float?
  approved                  Boolean              @default(false)
  created_at                DateTime             @default(now())
  updated_at                DateTime             @updatedAt
  updated_by                String?              @db.Uuid
  user                      User                 @relation(fields: [id], references: [id], onDelete: Cascade)
  qrLabels                  ShipmentQRLabel[]
  destShipments             Shipment[]           @relation("DestAO")
  originShipments           Shipment[]           @relation("OriginAO")
  pickupCarOperators        CarOperator[]        @relation("PickupAccessPoint")
  dropoffCarOperators       CarOperator[]        @relation("DropoffAccessPoint")
  updatedByUser             User?                @relation("AccessOperatorUpdatedBy", fields: [updated_by], references: [id])

  @@map("access_operators")
}

model CarOperator {
  id                       String          @id @db.Uuid
  license_number           String
  vehicle_info             String
  approved                 Boolean         @default(false)
  pickup_access_point_id   String?         @db.Uuid
  dropoff_access_point_id  String?         @db.Uuid
  created_at               DateTime        @default(now())
  updated_at               DateTime        @updatedAt
  updated_by               String?         @db.Uuid
  user                     User            @relation(fields: [id], references: [id], onDelete: Cascade)
  pickupAccessPoint        AccessOperator? @relation("PickupAccessPoint", fields: [pickup_access_point_id], references: [id])
  dropoffAccessPoint       AccessOperator? @relation("DropoffAccessPoint", fields: [dropoff_access_point_id], references: [id])
  updatedByUser            User?           @relation("CarOperatorUpdatedBy", fields: [updated_by], references: [id])

  @@map("car_operators")
}

model Shipment {
  id                      String                  @id @default(uuid()) @db.Uuid
  customer_id             String                  @db.Uuid
  origin_ao_id            String?                 @db.Uuid
  dest_ao_id              String?                 @db.Uuid
  assigned_car_operator_id String?                @db.Uuid
  status                  ShipmentStatus          @default(PENDING)
  weight                  Float
  size                    String
  description             String
  pickup_code             String
  tracking_code           String                  @unique @default(uuid())
  receiver_name           String
  receiver_phone          String
  estimated_delivery      DateTime?
  picked_up_at            DateTime?
  cancellation_reason     CancellationReason?
  cancelled_at            DateTime?
  expires_at              DateTime?
  created_at              DateTime                @default(now())
  updated_at              DateTime                @updatedAt
  updated_by              String?                 @db.Uuid
  auditLogs               AuditLog[]
  ManagementAction        ManagementAction[]
  qrLabels                ShipmentQRLabel[]
  statusHistory           ShipmentStatusHistory[]
  photos                  ShipmentPhoto[]
  notifications           Notification[]          @relation("ShipmentNotifications")
  adminNotifications      AdminNotification[]     @relation("AdminShipmentNotifications")
  customer                User                    @relation("CustomerShipments", fields: [customer_id], references: [id])
  assignedCarOperator     User?                   @relation("AssignedCarOperator", fields: [assigned_car_operator_id], references: [id])
  destAO                  AccessOperator?         @relation("DestAO", fields: [dest_ao_id], references: [id])
  originAO                AccessOperator?         @relation("OriginAO", fields: [origin_ao_id], references: [id])
  updatedByUser           User?                   @relation("ShipmentUpdatedBy", fields: [updated_by], references: [id])

  @@map("shipments")
}

model ShipmentQRLabel {
  id             String          @id @default(uuid()) @db.Uuid
  ao_id          String          @db.Uuid
  qr_value       String          @unique
  status         QRLabelStatus   @default(UNUSED)
  shipment_id    String?         @db.Uuid
  assigned_at    DateTime?
  created_at     DateTime        @default(now())
  updated_at     DateTime        @updatedAt
  updated_by     String?         @db.Uuid
  accessOperator AccessOperator  @relation(fields: [ao_id], references: [id])
  shipment       Shipment?       @relation(fields: [shipment_id], references: [id])
  updatedByUser  User?           @relation("QRLabelUpdatedBy", fields: [updated_by], references: [id])

  @@map("shipment_qr_labels")
}

model ShipmentPhoto {
  id          String                @id @default(uuid()) @db.Uuid
  shipment_id String                @db.Uuid
  user_id     String                @db.Uuid
  photo_url   String
  action      ShipmentPhotoAction
  notes       String?
  latitude    Float?
  longitude   Float?
  created_at  DateTime              @default(now())
  shipment    Shipment              @relation(fields: [shipment_id], references: [id], onDelete: Cascade)
  user        User                  @relation("ShipmentPhotos", fields: [user_id], references: [id])

  @@map("shipment_photos")
}

model ShipmentStatusHistory {
  id            String         @id @default(uuid()) @db.Uuid
  shipment_id   String         @db.Uuid
  status        ShipmentStatus
  changed_by_id String?        @db.Uuid
  changed_at    DateTime       @default(now())
  notes         String?
  user          User?          @relation(fields: [changed_by_id], references: [id])
  shipment      Shipment       @relation(fields: [shipment_id], references: [id])

  @@map("shipment_status_history")
}

model AuditLog {
  id          String    @id @default(uuid()) @db.Uuid
  user_id     String?   @db.Uuid
  admin_id    String?   @db.Uuid
  action      String
  shipment_id String?   @db.Uuid
  details     Json
  created_at  DateTime  @default(now())
  admin       Admin?    @relation(fields: [admin_id], references: [id])
  shipment    Shipment? @relation(fields: [shipment_id], references: [id])
  user        User?     @relation(fields: [user_id], references: [id])

  @@map("audit_logs")
}

model LoginAttempt {
  id           String   @id @default(uuid()) @db.Uuid
  user_id      String?  @db.Uuid
  admin_id     String?  @db.Uuid
  ip_address   String
  device       String?
  location     String?
  successful   Boolean
  attempted_at DateTime @default(now())
  admin        Admin?   @relation(fields: [admin_id], references: [id])
  user         User?    @relation(fields: [user_id], references: [id])

  @@map("login_attempts")
}

model SecurityEvent {
  id         String   @id @default(uuid()) @db.Uuid
  user_id    String?  @db.Uuid
  admin_id   String?  @db.Uuid
  event_type String
  details    Json
  created_at DateTime @default(now())
  admin      Admin?   @relation(fields: [admin_id], references: [id])
  user       User?    @relation(fields: [user_id], references: [id])

  @@map("security_events")
}

model ManagementAction {
  id             String    @id @default(uuid()) @db.Uuid
  admin_id       String    @db.Uuid
  action_type    String
  target_user_id String?   @db.Uuid
  shipment_id    String?   @db.Uuid
  details        Json
  created_at     DateTime  @default(now())
  admin          Admin     @relation(fields: [admin_id], references: [id])
  shipment       Shipment? @relation(fields: [shipment_id], references: [id])
  user           User?     @relation(fields: [target_user_id], references: [id])

  @@map("management_actions")
}

model EmailVerification {
  id         String   @id @default(uuid()) @db.Uuid
  user_id    String   @db.Uuid
  token      String   @unique
  expires_at DateTime
  created_at DateTime @default(now())
  used       Boolean  @default(false)
  user       User     @relation(fields: [user_id], references: [id])

  @@map("email_verifications")
}

model UserVerification {
  id         String   @id @default(uuid()) @db.Uuid
  user_id    String   @db.Uuid
  otp        String
  expires_at DateTime
  created_at DateTime @default(now())
  used       Boolean  @default(false)
  user       User     @relation(fields: [user_id], references: [id])

  @@map("user_verifications")
}

model PasswordReset {
  id         String   @id @default(uuid()) @db.Uuid
  user_id    String   @db.Uuid
  token      String   @unique
  expires_at DateTime
  created_at DateTime @default(now())
  used       Boolean  @default(false)
  user       User     @relation(fields: [user_id], references: [id])

  @@map("password_resets")
}

model AdminVerification {
  id         String   @id @default(uuid()) @db.Uuid
  admin_id   String   @db.Uuid
  otp        String
  expires_at DateTime
  created_at DateTime @default(now())
  used       Boolean  @default(false)
  admin      Admin    @relation(fields: [admin_id], references: [id])

  @@map("admin_verifications")
}

model SystemSettings {
  id                     Int      @id @default(1)
  min_distance_km        Float    @default(2.0)
  max_shipments_per_day  Int      @default(10)
  max_shipments_per_user Int      @default(100)
  max_pending_shipments  Int      @default(5)
  require_photo_proof    Boolean  @default(true)
  max_failed_logins      Int      @default(5)
  review_period_hours    Int      @default(24)
  enable_2fa             Boolean  @default(true)
  gps_tolerance_meters   Int      @default(50)
  created_at             DateTime @default(now())
  updated_at             DateTime @updatedAt
  updated_by             String?  @db.Uuid
  updatedByUser          User?    @relation("SystemSettingsUpdatedBy", fields: [updated_by], references: [id])

  @@map("system_settings")
}

model Notification {
  id                String              @id @default(uuid()) @db.Uuid
  user_id           String              @db.Uuid
  shipment_id       String?             @db.Uuid
  notification_type NotificationType
  title             String
  message           String
  priority          NotificationPriority @default(NORMAL)
  read              Boolean             @default(false)
  read_at           DateTime?
  metadata          Json?               // Additional data like tracking codes, AO names, etc.
  expires_at        DateTime?           // Optional expiration for time-sensitive notifications
  created_at        DateTime            @default(now())
  updated_at        DateTime            @updatedAt
  user              User                @relation("UserNotifications", fields: [user_id], references: [id], onDelete: Cascade)
  shipment          Shipment?           @relation("ShipmentNotifications", fields: [shipment_id], references: [id], onDelete: Cascade)

  @@index([user_id, read])
  @@index([user_id, created_at])
  @@index([shipment_id])
  @@map("notifications")
}

model AdminNotification {
  id                String              @id @default(uuid()) @db.Uuid
  admin_id          String              @db.Uuid
  shipment_id       String?             @db.Uuid
  notification_type NotificationType
  title             String
  message           String
  priority          NotificationPriority @default(NORMAL)
  read              Boolean             @default(false)
  read_at           DateTime?
  metadata          Json?               // Additional data like user info, system details, etc.
  expires_at        DateTime?           // Optional expiration for time-sensitive notifications
  created_at        DateTime            @default(now())
  updated_at        DateTime            @updatedAt
  admin             Admin               @relation("AdminNotifications", fields: [admin_id], references: [id], onDelete: Cascade)
  shipment          Shipment?           @relation("AdminShipmentNotifications", fields: [shipment_id], references: [id], onDelete: Cascade)

  @@index([admin_id, read])
  @@index([admin_id, created_at])
  @@index([shipment_id])
  @@map("admin_notifications")
}

model NotificationPreference {
  id                    String   @id @default(uuid()) @db.Uuid
  user_id               String   @db.Uuid
  email_notifications   Boolean  @default(true)
  sms_notifications     Boolean  @default(false)
  push_notifications    Boolean  @default(true)
  shipment_created      Boolean  @default(true)
  shipment_status_change Boolean @default(true)
  qr_assignment         Boolean  @default(true)
  package_ready         Boolean  @default(true)
  delivery_completed    Boolean  @default(true)
  preferred_language    Language @default(ENGLISH)
  email_format          EmailFormat @default(BILINGUAL)
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
  user                  User     @relation("UserNotificationPreferences", fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id])
  @@map("notification_preferences")
}

enum UserType {
  CUSTOMER
  ACCESS_OPERATOR
  CAR_OPERATOR
  ADMIN
}

enum UserStatus {
  ACTIVE
  PENDING
  SUSPENDED
  INACTIVE
}

enum AdminRole {
  ADMIN
  AUDITOR
  SUPPORT
}

enum ShipmentStatus {
  PENDING                    // Customer created shipment, 24h timer active
  AWAITING_PICKUP           // Customer arrived at origin AO, QR assigned, timer stopped
  PICKED_UP_BY_CO           // Car Operator picked up from origin AO
  IN_TRANSIT                // Car Operator is transporting to destination AO
  ARRIVED_AT_DESTINATION    // Car Operator arrived at destination AO
  READY_FOR_DELIVERY        // Package ready for customer pickup at destination
  DELIVERED                 // Customer picked up package
  CANCELLED                 // Shipment cancelled
}

enum CancellationReason {
  USER_CANCELLED
  SYSTEM_EXPIRED
  ADMIN_CANCELLED
}

enum QRLabelStatus {
  UNUSED
  ASSIGNED
  USED
}

enum ShipmentPhotoAction {
  QR_ASSIGNMENT     // When AO assigns QR to shipment
  DROPOFF           // When AO receives package from customer
  PICKUP            // When CO picks up package from origin AO
  ARRIVAL           // When CO arrives at destination AO
  DELIVERY          // When destination AO delivers to receiver
}

enum NotificationType {
  // User/Shipment notifications
  SHIPMENT_CREATED              // When customer creates new shipment
  SHIPMENT_ASSIGNED_QR          // When AO assigns QR to shipment
  SHIPMENT_DROPPED_OFF          // When customer drops off package at origin AO
  SHIPMENT_PICKED_UP            // When CO picks up package from origin AO
  SHIPMENT_IN_TRANSIT           // When CO starts transport
  SHIPMENT_ARRIVED              // When CO arrives at destination AO
  SHIPMENT_READY_FOR_DELIVERY   // When package is ready for customer pickup
  SHIPMENT_DELIVERED            // When package is delivered to receiver
  SHIPMENT_CANCELLED            // When shipment is cancelled
  SHIPMENT_EXPIRED              // When shipment expires
  SHIPMENT_STATUS_CHANGED       // General status change notification
  QR_CODE_ASSIGNED              // When QR code is assigned to shipment
  PACKAGE_READY_FOR_PICKUP      // When package is ready for CO pickup
  DELIVERY_REMINDER             // Reminder for pending delivery
  SYSTEM_ALERT                  // System-wide alerts

  // Admin-specific notifications
  USER_REGISTERED               // When new user registers
  USER_EMAIL_VERIFIED           // When user verifies email
  OPERATOR_NEEDS_APPROVAL       // When AO/CO needs admin approval
  USER_STATUS_CHANGED           // When user status is changed by admin
  SECURITY_ALERT                // Security-related alerts (failed logins, etc.)
  SYSTEM_ERROR                  // System errors requiring admin attention
  ADMIN_CREATED                 // When new admin is created
  BULK_OPERATION_COMPLETED      // When bulk operations complete
  SYSTEM_MAINTENANCE            // System maintenance notifications
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum Language {
  ENGLISH
  ARABIC
}

enum EmailFormat {
  ENGLISH_ONLY
  ARABIC_ONLY
  BILINGUAL
}

enum AdminActionType {
  USER_ACTIVATION
  USER_DEACTIVATION
  OPERATOR_APPROVAL
  OPERATOR_REJECTION
}

model AdminAction {
  id             String          @id @default(uuid()) @db.Uuid
  admin_id       String          @db.Uuid
  target_user_id String          @db.Uuid
  action_type    AdminActionType
  reason         String
  metadata       Json?
  created_at     DateTime        @default(now())
  updated_at     DateTime        @updatedAt

  admin       Admin @relation("AdminActions", fields: [admin_id], references: [id], onDelete: Cascade)
  target_user User  @relation("UserAdminActions", fields: [target_user_id], references: [id], onDelete: Cascade)

  @@index([admin_id])
  @@index([target_user_id])
  @@index([action_type])
  @@index([created_at])
  @@map("admin_actions")
}