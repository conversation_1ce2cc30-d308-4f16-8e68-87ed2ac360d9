/* eslint-disable no-nested-ternary */
import { AdminRole, UserStatus, ShipmentStatus } from "@prisma/client";
import { Request, Response } from "express";

import {
  AdminCreateSchema,
  AdminLoginSchema,
  AdminUpdateSchema,
  AdminChangePasswordSchema,
  AdminForgotPasswordSchema,
  AdminResetPasswordSchema,
  AdminChangeStatusSchema,
  AdminVerifyOTPSchema,
  UpdateUserStatusSchema,
  UserActivationApprovalSchema,
  GetAllNotificationsQuerySchema,
  BroadcastNotificationBodySchema,
  CreateNotificationTemplateBodySchema,
  UpdateNotificationTemplateBodySchema,
} from "../schemas/admin";
import { DashboardService } from "../services/DashboardService";
import { ServiceFactory } from "../services/ServiceFactory";
import { UserType } from "../types/models";
import { AppError } from "../utils/errors";
import { parsePaginationParams } from "../utils/pagination";

export class Admin<PERSON>ontroller {
  /**
   * Validate UUID format
   */
  private static validateUUID(id: string, fieldName: string = "ID"): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!id || !uuidRegex.test(id)) {
      throw AppError.badRequest(`Invalid ${fieldName} format`);
    }
  }

  /**
   * Register a new admin
   */
  static async registerAdmin(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { name, email, password, role } = AdminCreateSchema.parse(req.body);

      // Create admin
      const adminService = ServiceFactory.getAdminService();
      await adminService.registerAdmin(
        name,
        email,
        password,
        (role as AdminRole) || AdminRole.ADMIN
      );

      // Return success response with message to check email
      res.status(201).json({
        success: true,
        message:
          "Registration successful. Please check your email for login information.",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Login admin
   */
  static async loginAdmin(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { email, password } = AdminLoginSchema.parse(req.body);

      // Get IP address
      const ipAddress = req.ip || req.socket.remoteAddress || "unknown";

      // Login admin
      const adminService = ServiceFactory.getAdminService();
      const { admin, token } = await adminService.loginAdmin(
        email,
        password,
        ipAddress
      );

      // Return success response
      res.status(200).json({
        success: true,
        message: "Admin logged in successfully",
        data: {
          admin,
          token,
        },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get admin profile
   */
  static async getAdminProfile(req: Request, res: Response): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Get admin profile
      const adminService = ServiceFactory.getAdminService();
      const admin = await adminService.getAdminById(adminId);

      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: admin,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update admin profile
   */
  static async updateAdminProfile(req: Request, res: Response): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Validate request body
      const { name, role } = AdminUpdateSchema.parse(req.body);

      // Update admin
      const adminService = ServiceFactory.getAdminService();
      const updatedAdmin = await adminService.updateAdmin(adminId, {
        name,
        role: role as AdminRole,
      });

      // Return success response
      res.status(200).json({
        success: true,
        message: "Admin profile updated successfully",
        data: updatedAdmin,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Change admin password
   */
  static async changePassword(req: Request, res: Response): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Validate request body
      const { currentPassword, newPassword } = AdminChangePasswordSchema.parse(
        req.body
      );

      // Change password
      const adminService = ServiceFactory.getAdminService();
      await adminService.changePassword(adminId, currentPassword, newPassword);

      // Return success response
      res.status(200).json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Forgot password - send reset token
   */
  static async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { email } = AdminForgotPasswordSchema.parse(req.body);

      // Process forgot password request
      const adminService = ServiceFactory.getAdminService();
      await adminService.forgotPassword(email);

      // Return success response (even if admin doesn't exist for security)
      res.status(200).json({
        success: true,
        message:
          "If your email is registered, you will receive a password reset link",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { email, token, password } = AdminResetPasswordSchema.parse(
        req.body
      );

      // Reset password
      const adminService = ServiceFactory.getAdminService();
      await adminService.resetPassword(email, token, password);

      // Return success response
      res.status(200).json({
        success: true,
        message: "Password reset successfully",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get all admins (admin only)
   */
  static async getAllAdmins(req: Request, res: Response): Promise<void> {
    try {
      // Parse pagination parameters
      const { paginationOptions, paginationMeta } = parsePaginationParams(
        req.query
      );
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;

      // Get query parameters
      const { search, role, status } = req.query;

      // Get all admins
      const adminService = ServiceFactory.getAdminService();
      const { admins, total } = await adminService.getAllAdmins({
        skip,
        take,
        search: search as string,
        role: role as AdminRole,
        status: status as UserStatus,
      });

      // Return success response with consistent pagination
      res.status(200).json({
        success: true,
        data: {
          admins,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: (page + 1) * limit < total,
            hasPrev: page > 0,
          },
        },
      });
    } catch (error) {
      if (
        error instanceof Error &&
        (error.message.includes("Page number") ||
          error.message.includes("Limit must"))
      ) {
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: error.message,
        });
        return;
      }
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Change admin status (activate/deactivate)
   */
  static async changeAdminStatus(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { id, status } = AdminChangeStatusSchema.parse(req.body);

      // Change admin status
      const adminService = ServiceFactory.getAdminService();
      const updatedAdmin = await adminService.changeAdminStatus(id, status);

      // Return success response
      res.status(200).json({
        success: true,
        message: `Admin ${
          status === UserStatus.ACTIVE ? "activated" : "deactivated"
        } successfully`,
        data: updatedAdmin,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Verify admin OTP
   */
  static async verifyAdminOTP(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { email, otp } = AdminVerifyOTPSchema.parse(req.body);

      // Verify OTP
      const adminService = ServiceFactory.getAdminService();
      await adminService.verifyAdminOTP(email, otp);

      // Return success response
      res.status(200).json({
        success: true,
        message: "Email verified successfully. You can now log in.",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Resend admin OTP
   */
  static async resendAdminOTP(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { email } = AdminForgotPasswordSchema.parse(req.body);

      // Resend OTP
      const adminService = ServiceFactory.getAdminService();
      await adminService.resendAdminOTP(email);

      // Return success response
      res.status(200).json({
        success: true,
        message: "OTP sent successfully. Please check your email.",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Refresh admin token
   */
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Refresh token
      const adminService = ServiceFactory.getAdminService();
      const { admin, token } = await adminService.refreshToken(adminId);

      // Return success response
      res.status(200).json({
        success: true,
        message: "Token refreshed successfully",
        data: {
          admin,
          token,
        },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Logout admin
   */
  static async logoutAdmin(req: Request, res: Response): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Logout admin
      const adminService = ServiceFactory.getAdminService();
      await adminService.logoutAdmin(adminId);

      // Return success response
      res.status(200).json({
        success: true,
        message: "Admin logged out successfully",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get admin dashboard data
   */
  static async getAdminDashboard(req: Request, res: Response): Promise<void> {
    try {
      // Get admin information from the authenticated request
      const adminId = req.user?.id;
      const adminEmail = req.user?.email;

      if (!adminId || !adminEmail) {
        throw AppError.unauthorized("Invalid admin token payload");
      }

      // Get admin details from database
      const adminService = ServiceFactory.getAdminService();
      const admin = await adminService.getAdminById(adminId);

      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      // Get dashboard service
      const prisma = ServiceFactory.getPrismaClient();
      const dashboardService = new DashboardService(prisma);

      // Get admin dashboard data
      const dashboardData = await dashboardService.getDashboardData(adminId, UserType.ADMIN);

      // Prepare response
      const response = {
        admin_info: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          email_verified: admin.email_verified,
        },
        dashboard_data: dashboardData,
      };

      res.status(200).json({
        success: true,
        message: "Admin dashboard data retrieved successfully",
        data: response,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update user status (ACTIVE/INACTIVE)
   */
  static async updateUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const { userId, status } = UpdateUserStatusSchema.parse(req.body);
      const adminId = req.user?.id;

      // Get admin service
      const adminService = ServiceFactory.getAdminService();

      // Update user status
      const updatedUser = await adminService.changeUserStatus(userId, status, adminId);

      res.status(200).json({
        success: true,
        message: `User status updated to ${status}`,
        data: {
          user: {
            id: updatedUser.id,
            name: updatedUser.name,
            email: updatedUser.email,
            status: updatedUser.status,
          }
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Unified user activation and approval endpoint
   */
  static async updateUserActivationApproval(req: Request, res: Response): Promise<void> {
    try {
      // Get user ID from URL parameters
      const { id: userId } = req.params;
      
      // Validate UUID format
      AdminController.validateUUID(userId, "User ID");

      // Validate request body using schema
      const { active, approved, reason } = UserActivationApprovalSchema.parse(req.body);

      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Get user service
      const userService = ServiceFactory.getUserService();

      // Process user activation and approval
      const result = await userService.processUserActivationApproval(
        userId,
        active,
        approved,
        adminId,
        reason
      );

      // Format success message based on changes made
      let message = "User activation and approval processed successfully";
      const changes: string[] = [];
      
      if (result.activation.changed) {
        changes.push(`${result.activation.active ? 'activated' : 'deactivated'}`);
      }
      
      if (result.approval.changed && result.approval.applicable) {
        changes.push(`${result.approval.approved ? 'approved' : 'rejected'}`);
      }
      
      if (changes.length > 0) {
        message = `User ${changes.join(' and ')} successfully`;
      }

      // Return success response
      res.status(200).json({
        success: true,
        message,
        data: result,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update operator approval status
   */
  static async updateOperatorApproval(req: Request, res: Response): Promise<void> {
    try {
      const { userId, approval_status, approved } = req.body;
      const adminId = req.user?.id;

      if (!userId) {
        throw AppError.badRequest("User ID is required");
      }

      // Handle both approval_status (string) and approved (boolean) parameters for backward compatibility
      let approvedBoolean: boolean;

      if (approval_status !== undefined) {
        // Handle string approval_status parameter (as documented)
        if (!['APPROVED', 'REJECTED'].includes(approval_status)) {
          throw AppError.badRequest("approval_status must be APPROVED or REJECTED");
        }
        approvedBoolean = approval_status === 'APPROVED';
      } else if (approved !== undefined) {
        // Handle boolean approved parameter (for backward compatibility)
        if (typeof approved !== 'boolean') {
          throw AppError.badRequest("approved must be a boolean");
        }
        approvedBoolean = approved;
      } else {
        throw AppError.badRequest("Either approval_status or approved parameter is required");
      }

      // Get user service
      const userService = ServiceFactory.getUserService();

      // Update operator approval
      const result = await userService.updateOperatorApproval(userId, approvedBoolean);

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        userId,  // The user being modified
        "OPERATOR_APPROVAL_CHANGED_BY_ADMIN",
        undefined,
        {
          user_id: userId,
          approved: approvedBoolean,
          approval_status: approvedBoolean ? 'APPROVED' : 'REJECTED',
          admin_id: adminId
        },
        adminId  // The admin performing the action
      );

      res.status(200).json({
        success: true,
        message: `Operator ${approvedBoolean ? 'approved' : 'rejected'} successfully`,
        data: result
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Comprehensive user management - update both status and approval
   */
  static async updateUserManagement(req: Request, res: Response): Promise<void> {
    try {
      const { userId, status, approved, reason } = req.body;
      const adminId = req.user?.id;

      if (!userId) {
        throw AppError.badRequest("User ID is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Get user with operator data
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          accessOperator: true,
          carOperator: true
        }
      });

      if (!user) {
        throw AppError.notFound("User not found");
      }

      const updates: any = {};
      const auditDetails: any = { user_id: userId, admin_id: adminId };

      // Update user status if provided
      if (status !== undefined) {
        if (!Object.values(UserStatus).includes(status)) {
          throw AppError.badRequest("Invalid status value");
        }
        updates.status = status;
        auditDetails.new_status = status;
        auditDetails.old_status = user.status;
      }

      // Update operator approval if provided and user is an operator
      if (approved !== undefined && (user.user_type === UserType.ACCESS_OPERATOR || user.user_type === UserType.CAR_OPERATOR)) {
        if (typeof approved !== 'boolean') {
          throw AppError.badRequest("approved must be a boolean");
        }
        auditDetails.approved = approved;
        auditDetails.approval_status = approved ? 'APPROVED' : 'REJECTED';
      }

      if (reason) {
        auditDetails.reason = reason;
      }

      // Perform updates in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Update user status if needed
        let updatedUser = user;
        if (Object.keys(updates).length > 0) {
          const userUpdate = await tx.user.update({
            where: { id: userId },
            data: { ...updates, updated_by: adminId }
          });
          // Merge the updated user data with the original operator data
          updatedUser = { ...user, ...userUpdate };
        }

        // Update operator approval if needed
        if (approved !== undefined) {
          if (user.user_type === UserType.ACCESS_OPERATOR && user.accessOperator) {
            await tx.accessOperator.update({
              where: { id: userId },
              data: { approved, updated_by: adminId }
            });
          } else if (user.user_type === UserType.CAR_OPERATOR && user.carOperator) {
            await tx.carOperator.update({
              where: { id: userId },
              data: { approved, updated_by: adminId }
            });
          }
        }

        return updatedUser;
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        userId,  // The user being modified
        "USER_MANAGEMENT_UPDATED_BY_ADMIN",
        undefined,
        auditDetails,
        adminId  // The admin performing the action
      );

      res.status(200).json({
        success: true,
        message: "User management updated successfully",
        data: {
          user: {
            id: result.id,
            name: result.name,
            email: result.email,
            status: result.status,
            user_type: result.user_type
          },
          updates: auditDetails
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get expired shipment statistics
   */
  static async getExpiredShipmentStats(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      // Get admin ID from authenticated request
      const adminId = req.user?.id;

      if (!adminId) {
        throw AppError.unauthorized("Not authenticated");
      }

      // Get expired shipment statistics
      const scheduledJobService = ServiceFactory.getScheduledJobService();
      const stats = await scheduledJobService.getExpiredShipmentStats();

      // Return success response
      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // ANALYTICS & DASHBOARD METHODS
  // ============================================================================

  /**
   * Get system overview analytics
   */
  static async getSystemOverview(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      // Get comprehensive system statistics
      const [
        totalUsers,
        activeUsers,
        totalShipments,
        activeShipments,
        deliveredShipments,
        totalAdmins,
        systemUptime
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { status: UserStatus.ACTIVE } }),
        prisma.shipment.count(),
        prisma.shipment.count({
          where: {
            status: {
              in: [ShipmentStatus.PENDING, ShipmentStatus.AWAITING_PICKUP, ShipmentStatus.IN_TRANSIT]
            }
          }
        }),
        prisma.shipment.count({ where: { status: ShipmentStatus.DELIVERED } }),
        prisma.admin.count({ where: { status: UserStatus.ACTIVE } }),
        Promise.resolve(process.uptime())
      ]);

      const systemStats = {
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers
        },
        shipments: {
          total: totalShipments,
          active: activeShipments,
          delivered: deliveredShipments,
          delivery_rate: totalShipments > 0 ? Math.round((deliveredShipments / totalShipments) * 100) : 0
        },
        system: {
          admins: totalAdmins,
          uptime_seconds: Math.floor(systemUptime),
          uptime_hours: Math.floor(systemUptime / 3600)
        }
      };

      res.status(200).json({
        success: true,
        message: "System overview retrieved successfully",
        data: systemStats
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get user analytics
   */
  static async getUserAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      // Get user analytics data
      const [
        usersByType,
        usersByStatus,
        recentRegistrations,
        pendingApprovals
      ] = await Promise.all([
        prisma.user.groupBy({
          by: ['user_type'],
          _count: { id: true }
        }),
        prisma.user.groupBy({
          by: ['status'],
          _count: { id: true }
        }),
        prisma.user.count({
          where: {
            created_at: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          }
        }),
        Promise.all([
          prisma.accessOperator.count({ where: { approved: false } }),
          prisma.carOperator.count({ where: { approved: false } })
        ]).then(([ao, co]) => ao + co)
      ]);

      const analytics = {
        by_type: usersByType.map(item => ({
          type: item.user_type,
          count: item._count.id
        })),
        by_status: usersByStatus.map(item => ({
          status: item.status,
          count: item._count.id
        })),
        recent_registrations: recentRegistrations,
        pending_approvals: pendingApprovals
      };

      res.status(200).json({
        success: true,
        message: "User analytics retrieved successfully",
        data: analytics
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get shipment analytics
   */
  static async getShipmentAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      // Get shipment analytics data
      const [
        shipmentsByStatus,
        recentShipments,
        expiredShipments,
      ] = await Promise.all([
        prisma.shipment.groupBy({
          by: ['status'],
          _count: { id: true }
        }),
        prisma.shipment.count({
          where: {
            created_at: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          }
        }),
        prisma.shipment.count({
          where: {
            expires_at: {
              lt: new Date()
            },
            status: {
              notIn: [ShipmentStatus.DELIVERED, ShipmentStatus.CANCELLED]
            }
          }
        }),
        prisma.shipment.aggregate({
          where: {
            status: ShipmentStatus.DELIVERED,
            updated_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          },
          _avg: {
            // This would need a calculated field for delivery time
            // For now, we'll use a placeholder
          }
        })
      ]);

      const analytics = {
        by_status: shipmentsByStatus.map(item => ({
          status: item.status,
          count: item._count.id
        })),
        recent_shipments: recentShipments,
        expired_shipments: expiredShipments,
        performance: {
          average_delivery_hours: 24, // Placeholder - would need proper calculation
          on_time_delivery_rate: 85 // Placeholder - would need proper calculation
        }
      };

      res.status(200).json({
        success: true,
        message: "Shipment analytics retrieved successfully",
        data: analytics
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get performance metrics
   */
  static async getPerformanceMetrics(req: Request, res: Response): Promise<void> {
    try {
      // Get performance metrics
      const [
        avgResponseTime,
        errorRate,
        activeConnections,
        memoryUsage
      ] = await Promise.all([
        // These would typically come from monitoring systems
        Promise.resolve(150), // ms - placeholder
        Promise.resolve(0.5), // % - placeholder
        Promise.resolve(25), // active connections - placeholder
        Promise.resolve(process.memoryUsage())
      ]);

      const metrics = {
        response_time: {
          average_ms: avgResponseTime,
          status: avgResponseTime < 200 ? 'good' : avgResponseTime < 500 ? 'warning' : 'critical'
        },
        error_rate: {
          percentage: errorRate,
          status: errorRate < 1 ? 'good' : errorRate < 5 ? 'warning' : 'critical'
        },
        system: {
          active_connections: activeConnections,
          memory_usage_mb: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          memory_total_mb: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          cpu_usage: 0 // Placeholder - would need proper monitoring
        }
      };

      res.status(200).json({
        success: true,
        message: "Performance metrics retrieved successfully",
        data: metrics
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get system health status
   */
  static async getSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      // Check various system components
      const healthChecks = await Promise.allSettled([
        // Database connectivity
        prisma.$queryRaw`SELECT 1`,

        // Check if critical tables exist and are accessible
        prisma.user.count(),
        prisma.shipment.count(),
        prisma.admin.count(),

        // Check system settings
        prisma.systemSettings.findFirst()
      ]);

      const dbHealth = healthChecks[0].status === 'fulfilled' ? 'healthy' : 'unhealthy';
      const tablesHealth = healthChecks.slice(1, 4).every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy';
      const settingsHealth = healthChecks[4].status === 'fulfilled' ? 'healthy' : 'unhealthy';

      const overallHealth = [dbHealth, tablesHealth, settingsHealth].every(status => status === 'healthy') ? 'healthy' : 'unhealthy';

      const healthStatus = {
        overall: overallHealth,
        components: {
          database: {
            status: dbHealth,
            response_time_ms: 50 // Placeholder
          },
          tables: {
            status: tablesHealth,
            accessible: healthChecks.slice(1, 4).filter(check => check.status === 'fulfilled').length
          },
          settings: {
            status: settingsHealth,
            configured: healthChecks[4].status === 'fulfilled'
          },
          memory: {
            status: process.memoryUsage().heapUsed < 500 * 1024 * 1024 ? 'healthy' : 'warning',
            usage_mb: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
          }
        },
        timestamp: new Date().toISOString(),
        uptime_seconds: Math.floor(process.uptime())
      };

      res.status(200).json({
        success: true,
        message: "System health status retrieved successfully",
        data: healthStatus
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // ENHANCED USER MANAGEMENT METHODS
  // ============================================================================

  /**
   * Get all users with enhanced filtering and pagination
   */
  static async getAllUsersEnhanced(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const { search, user_type, status, approval_status, sort = 'created_at', order = 'desc' } = req.query;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search as string, mode: 'insensitive' } },
          { email: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      if (user_type) {
        where.user_type = user_type;
      }

      if (status) {
        where.status = status;
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Get users with complete related data
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take,
          orderBy: { [sort as string]: order },
          include: {
            accessOperator: {
              select: {
                id: true,
                business_name: true,
                address: true,
                geo_latitude: true,
                geo_longitude: true,
                approved: true,
                created_at: true,
                updated_at: true,
                updated_by: true
              }
            },
            carOperator: {
              select: {
                id: true,
                license_number: true,
                vehicle_info: true,
                approved: true,
                pickup_access_point_id: true,
                dropoff_access_point_id: true,
                created_at: true,
                updated_at: true,
                updated_by: true
              }
            },
            _count: {
              select: {
                shipments: true,
                auditLogs: true,
                emailVerifications: true,
                userVerifications: true,
                loginAttempts: true,
                passwordResets: true,
                securityEvents: true
              }
            },
            // Include recent activity for admin monitoring
            auditLogs: {
              take: 5,
              orderBy: { created_at: 'desc' },
              select: {
                id: true,
                action: true,
                created_at: true,
                details: true
              }
            },
            loginAttempts: {
              take: 3,
              orderBy: { attempted_at: 'desc' },
              select: {
                id: true,
                successful: true,
                attempted_at: true,
                ip_address: true
              }
            }
          }
        }),
        prisma.user.count({ where })
      ]);

      // Filter by approval status if specified
      let filteredUsers = users;
      if (approval_status) {
        filteredUsers = users.filter(user => {
          const userWithRelations = user as any; // Type assertion to access included relations
          if (user.user_type === UserType.ACCESS_OPERATOR && userWithRelations.accessOperator) {
            return approval_status === 'APPROVED' ? userWithRelations.accessOperator.approved : !userWithRelations.accessOperator.approved;
          }
          if (user.user_type === UserType.CAR_OPERATOR && userWithRelations.carOperator) {
            return approval_status === 'APPROVED' ? userWithRelations.carOperator.approved : !userWithRelations.carOperator.approved;
          }
          return approval_status === 'APPROVED'; // Customers are auto-approved
        });
      }

      // Remove sensitive data and format response
      const sanitizedUsers = filteredUsers.map(user => {
        // Remove password_hash (sensitive field)
        const { password_hash: _password_hash, ...userWithoutSensitiveData } = user;

        // Add approval status for easier frontend handling
        let approval_status = 'APPROVED'; // Default for customers and admins
        if (user.user_type === UserType.ACCESS_OPERATOR && user.accessOperator) {
          approval_status = user.accessOperator.approved ? 'APPROVED' : 'PENDING';
        } else if (user.user_type === UserType.CAR_OPERATOR && user.carOperator) {
          approval_status = user.carOperator.approved ? 'APPROVED' : 'PENDING';
        }

        return {
          ...userWithoutSensitiveData,
          approval_status
        };
      });

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Users retrieved successfully",
        data: {
          users: sanitizedUsers,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: (page + 1) * limit < total,
            hasPrev: page > 0,
          }
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get user by ID with enhanced details - includes comprehensive information for all user types
   */
  static async getUserByIdEnhanced(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      AdminController.validateUUID(id, "user ID");

      const prisma = ServiceFactory.getPrismaClient();

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          accessOperator: true,
          carOperator: {
            include: {
              pickupAccessPoint: {
                select: {
                  id: true,
                  business_name: true,
                  address: true,
                  geo_latitude: true,
                  geo_longitude: true
                }
              },
              dropoffAccessPoint: {
                select: {
                  id: true,
                  business_name: true,
                  address: true,
                  geo_latitude: true,
                  geo_longitude: true
                }
              }
            }
          },
          shipments: {
            take: 10,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              status: true,
              created_at: true,
              tracking_code: true,
              weight: true,
              size: true,
              description: true,
              receiver_name: true,
              receiver_phone: true
            }
          },
          auditLogs: {
            take: 10,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              action: true,
              created_at: true,
              details: true
            }
          },
          emailVerifications: {
            take: 5,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              created_at: true,
              used: true,
              expires_at: true
            }
          },
          loginAttempts: {
            take: 10,
            orderBy: { attempted_at: 'desc' },
            select: {
              id: true,
              successful: true,
              ip_address: true,
              attempted_at: true
            }
          },
          securityEvents: {
            take: 10,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              event_type: true,
              created_at: true,
              details: true
            }
          },
          passwordResets: {
            take: 5,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              created_at: true,
              used: true,
              expires_at: true
            }
          },
          userVerifications: {
            take: 5,
            orderBy: { created_at: 'desc' },
            select: {
              id: true,
              created_at: true,
              used: true,
              expires_at: true
            }
          },
          _count: {
            select: {
              shipments: true,
              auditLogs: true,
              emailVerifications: true,
              userVerifications: true,
              loginAttempts: true,
              passwordResets: true,
              securityEvents: true
            }
          }
        }
      });

      if (!user) {
        throw AppError.notFound("User not found");
      }

      // Get notification preferences
      let notificationPreferences = null;
      try {
        notificationPreferences = await prisma.notificationPreference.findUnique({
          where: { user_id: id }
        });
      } catch (error) {
        console.warn("Could not fetch notification preferences:", error);
      }

      // Remove password_hash (sensitive field)
      const { password_hash: _password_hash, ...userWithoutSensitiveData } = user;

      // Add approval status for easier frontend handling
      let approval_status = 'APPROVED'; // Default for customers and admins
      if (user.user_type === UserType.ACCESS_OPERATOR && user.accessOperator) {
        approval_status = user.accessOperator.approved ? 'APPROVED' : 'PENDING';
      } else if (user.user_type === UserType.CAR_OPERATOR && user.carOperator) {
        approval_status = user.carOperator.approved ? 'APPROVED' : 'PENDING';
      }

      const responseData = {
        ...userWithoutSensitiveData,
        approval_status,
        notificationPreferences
      };

      res.status(200).json({
        success: true,
        message: "User details retrieved successfully",
        data: responseData
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update user information
   */
  static async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, phone, status } = req.body;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "user ID");

      const prisma = ServiceFactory.getPrismaClient();

      // Check if user exists
      const existingUser = await prisma.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        throw AppError.notFound("User not found");
      }

      // Prepare update data
      const updateData: any = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;
      if (status) updateData.status = status;
      if (adminId) updateData.updated_by = adminId;

      // Update user
      const updatedUser = await prisma.user.update({
        where: { id },
        data: updateData
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "USER_UPDATED_BY_ADMIN",
        undefined,
        {
          user_id: id,
          changes: updateData,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "User updated successfully",
        data: updatedUser
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Bulk approve operators
   */
  static async bulkApproveOperators(req: Request, res: Response): Promise<void> {
    try {
      const { user_ids } = req.body;
      const adminId = req.user?.id;

      if (!Array.isArray(user_ids) || user_ids.length === 0) {
        throw AppError.badRequest("user_ids array is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Get users to approve
      const users = await prisma.user.findMany({
        where: {
          id: { in: user_ids },
          user_type: { in: [UserType.ACCESS_OPERATOR, UserType.CAR_OPERATOR] }
        },
        include: {
          accessOperator: true,
          carOperator: true
        }
      });

      if (users.length === 0) {
        throw AppError.notFound("No valid operators found");
      }

      // Bulk approve operations
      const results = await Promise.allSettled(
        users.map(async (user) => {
          if (user.user_type === UserType.ACCESS_OPERATOR && user.accessOperator) {
            return prisma.accessOperator.update({
              where: { id: user.id },
              data: { approved: true, updated_by: adminId }
            });
          } else if (user.user_type === UserType.CAR_OPERATOR && user.carOperator) {
            return prisma.carOperator.update({
              where: { id: user.id },
              data: { approved: true, updated_by: adminId }
            });
          }
        })
      );

      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "BULK_APPROVE_OPERATORS",
        undefined,
        {
          user_ids,
          successful,
          failed,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: `Bulk approval completed. ${successful} approved, ${failed} failed.`,
        data: {
          successful,
          failed,
          total: results.length
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Bulk reject operators
   */
  static async bulkRejectOperators(req: Request, res: Response): Promise<void> {
    try {
      const { user_ids } = req.body;
      const adminId = req.user?.id;

      if (!Array.isArray(user_ids) || user_ids.length === 0) {
        throw AppError.badRequest("user_ids array is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Update user status to suspended for rejected operators
      const result = await prisma.user.updateMany({
        where: {
          id: { in: user_ids },
          user_type: { in: [UserType.ACCESS_OPERATOR, UserType.CAR_OPERATOR] }
        },
        data: {
          status: UserStatus.SUSPENDED,
          updated_by: adminId
        }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "BULK_REJECT_OPERATORS",
        undefined,
        {
          user_ids,
          rejected_count: result.count,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: `${result.count} operators rejected successfully`,
        data: {
          rejected_count: result.count
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get user activity history
   */
  static async getUserActivity(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      AdminController.validateUUID(id, "user ID");

      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;

      const prisma = ServiceFactory.getPrismaClient();

      // Get user activity (audit logs)
      const [activities, total] = await Promise.all([
        prisma.auditLog.findMany({
          where: { user_id: id },
          skip,
          take,
          orderBy: { created_at: 'desc' },
          select: {
            id: true,
            action: true,
            details: true,
            created_at: true,
            shipment_id: true
          }
        }),
        prisma.auditLog.count({ where: { user_id: id } })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "User activity retrieved successfully",
        data: activities,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Send notification to specific user
   */
  static async notifyUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { title, message, type = 'SYSTEM', priority = 'NORMAL' } = req.body;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "user ID");

      if (!title || !message) {
        throw AppError.badRequest("Title and message are required");
      }

      const notificationService = ServiceFactory.getNotificationService();

      // Create notification
      const notification = await notificationService.createNotificationEnhanced({
        userId: id,
        type: type as any,
        title,
        message,
        priority: priority as any,
        metadata: {
          sent_by_admin: adminId,
          admin_initiated: true
        }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "ADMIN_SENT_NOTIFICATION",
        undefined,
        {
          user_id: id,
          notification_id: notification.id,
          title,
          admin_id: adminId
        },
        adminId
      );

      res.status(201).json({
        success: true,
        message: "Notification sent successfully",
        data: notification
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // ADMIN MANAGEMENT METHODS
  // ============================================================================

  /**
   * Create new admin
   */
  static async createAdmin(req: Request, res: Response): Promise<void> {
    try {
      const { name, email, password, role } = AdminCreateSchema.parse(req.body);
      const adminId = req.user?.id;

      const adminService = ServiceFactory.getAdminService();
      const newAdmin = await adminService.registerAdmin(
        name,
        email,
        password,
        (role as AdminRole) || AdminRole.ADMIN
      );

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "ADMIN_CREATED",
        undefined,
        {
          new_admin_id: newAdmin.id,
          new_admin_email: newAdmin.email,
          created_by: adminId
        },
        adminId
      );

      res.status(201).json({
        success: true,
        message: "Admin created successfully",
        data: {
          id: newAdmin.id,
          name: newAdmin.name,
          email: newAdmin.email,
          role: newAdmin.role,
          status: newAdmin.status
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get admin by ID
   */
  static async getAdminById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      AdminController.validateUUID(id, "admin ID");

      const prisma = ServiceFactory.getPrismaClient();

      const admin = await prisma.admin.findUnique({
        where: { id },
        include: {
          auditLogs: {
            take: 10,
            orderBy: { created_at: 'desc' },
            select: {
              action: true,
              created_at: true,
              details: true
            }
          },
          loginAttempts: {
            take: 10,
            orderBy: { attempted_at: 'desc' },
            select: {
              successful: true,
              ip_address: true,
              attempted_at: true
            }
          },
          _count: {
            select: {
              auditLogs: true,
              loginAttempts: true
            }
          }
        }
      });

      if (!admin) {
        throw AppError.notFound("Admin not found");
      }

      res.status(200).json({
        success: true,
        message: "Admin details retrieved successfully",
        data: admin
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update admin
   */
  static async updateAdmin(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      AdminController.validateUUID(id, "admin ID");

      const { name, role } = AdminUpdateSchema.parse(req.body);

      const adminService = ServiceFactory.getAdminService();
      const updatedAdmin = await adminService.updateAdmin(id, { name, role });

      res.status(200).json({
        success: true,
        message: "Admin updated successfully",
        data: updatedAdmin
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Deactivate admin
   */
  static async deactivateAdmin(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "admin ID");

      if (id === adminId) {
        throw AppError.badRequest("Cannot deactivate your own account");
      }

      const prisma = ServiceFactory.getPrismaClient();

      const updatedAdmin = await prisma.admin.update({
        where: { id },
        data: {
          status: UserStatus.SUSPENDED,
          updated_by: adminId
        }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "ADMIN_DEACTIVATED",
        undefined,
        {
          deactivated_admin_id: id,
          deactivated_by: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "Admin deactivated successfully",
        data: updatedAdmin
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // SHIPMENT MANAGEMENT METHODS
  // ============================================================================

  /**
   * Get all shipments with admin-level access
   */
  static async getAllShipments(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const { search, status, origin_ao_id, dest_ao_id, customer_id, sort = 'created_at', order = 'desc' } = req.query;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          { tracking_code: { contains: search as string, mode: 'insensitive' } },
          { description: { contains: search as string, mode: 'insensitive' } },
          { receiver_name: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      if (status) where.status = status;
      if (origin_ao_id) where.origin_ao_id = origin_ao_id;
      if (dest_ao_id) where.dest_ao_id = dest_ao_id;
      if (customer_id) where.customer_id = customer_id;

      const prisma = ServiceFactory.getPrismaClient();

      const [shipments, total] = await Promise.all([
        prisma.shipment.findMany({
          where,
          skip,
          take,
          orderBy: { [sort as string]: order },
          include: {
            customer: {
              select: { id: true, name: true, email: true }
            },
            originAO: {
              select: { id: true, business_name: true }
            },
            destAO: {
              select: { id: true, business_name: true }
            }
          }
        }),
        prisma.shipment.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Shipments retrieved successfully",
        data: shipments,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get shipment by ID with full details
   */
  static async getShipmentById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      AdminController.validateUUID(id, "shipment ID");

      const prisma = ServiceFactory.getPrismaClient();

      const shipment = await prisma.shipment.findUnique({
        where: { id },
        include: {
          customer: true,
          originAO: true,
          destAO: true,
          auditLogs: {
            orderBy: { created_at: 'desc' },
            include: {
              user: {
                select: { name: true, email: true }
              },
              admin: {
                select: { name: true, email: true }
              }
            }
          },
          qrLabels: true
        }
      });

      if (!shipment) {
        throw AppError.notFound("Shipment not found");
      }

      res.status(200).json({
        success: true,
        message: "Shipment details retrieved successfully",
        data: shipment
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update shipment (admin override)
   */
  static async updateShipment(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, notes, origin_ao_id, dest_ao_id } = req.body;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "shipment ID");

      const prisma = ServiceFactory.getPrismaClient();

      // Check if shipment exists
      const existingShipment = await prisma.shipment.findUnique({
        where: { id }
      });

      if (!existingShipment) {
        throw AppError.notFound("Shipment not found");
      }

      // Prepare update data
      const updateData: any = {};
      if (status) updateData.status = status;
      if (origin_ao_id) updateData.origin_ao_id = origin_ao_id;
      if (dest_ao_id) updateData.dest_ao_id = dest_ao_id;

      // Update shipment
      const updatedShipment = await prisma.shipment.update({
        where: { id },
        data: updateData
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "SHIPMENT_UPDATED_BY_ADMIN",
        id,
        {
          changes: updateData,
          notes,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "Shipment updated successfully",
        data: updatedShipment
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update shipment status
   */
  static async updateShipmentStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, reason } = req.body;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "shipment ID");

      if (!status) {
        throw AppError.badRequest("Status is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      const updatedShipment = await prisma.shipment.update({
        where: { id },
        data: { status }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "SHIPMENT_STATUS_CHANGED_BY_ADMIN",
        id,
        {
          new_status: status,
          reason,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "Shipment status updated successfully",
        data: updatedShipment
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Bulk cancel shipments
   */
  static async bulkCancelShipments(req: Request, res: Response): Promise<void> {
    try {
      const { shipment_ids, reason } = req.body;
      const adminId = req.user?.id;

      if (!Array.isArray(shipment_ids) || shipment_ids.length === 0) {
        throw AppError.badRequest("shipment_ids array is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Update shipments to cancelled status
      const result = await prisma.shipment.updateMany({
        where: {
          id: { in: shipment_ids },
          status: { notIn: [ShipmentStatus.DELIVERED, ShipmentStatus.CANCELLED] }
        },
        data: { status: ShipmentStatus.CANCELLED }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "BULK_CANCEL_SHIPMENTS",
        undefined,
        {
          shipment_ids,
          cancelled_count: result.count,
          reason,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: `${result.count} shipments cancelled successfully`,
        data: {
          cancelled_count: result.count,
          requested_count: shipment_ids.length
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get expired shipments
   */
  static async getExpiredShipments(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const prisma = ServiceFactory.getPrismaClient();

      const [expiredShipments, total] = await Promise.all([
        prisma.shipment.findMany({
          where: {
            expires_at: { lt: new Date() },
            status: { notIn: [ShipmentStatus.DELIVERED, ShipmentStatus.CANCELLED] }
          },
          skip,
          take,
          orderBy: { expires_at: 'asc' },
          include: {
            customer: {
              select: { id: true, name: true, email: true }
            },
            originAO: {
              select: { id: true, business_name: true }
            },
            destAO: {
              select: { id: true, business_name: true }
            }
          }
        }),
        prisma.shipment.count({
          where: {
            expires_at: { lt: new Date() },
            status: { notIn: [ShipmentStatus.DELIVERED, ShipmentStatus.CANCELLED] }
          }
        })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Expired shipments retrieved successfully",
        data: expiredShipments,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Reassign shipment to different operators
   */
  static async reassignShipment(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { new_origin_ao_id, new_dest_ao_id, reason } = req.body;
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "shipment ID");

      if (!new_origin_ao_id && !new_dest_ao_id) {
        throw AppError.badRequest("At least one new operator ID is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Get current shipment
      const currentShipment = await prisma.shipment.findUnique({
        where: { id }
      });

      if (!currentShipment) {
        throw AppError.notFound("Shipment not found");
      }

      // Prepare update data
      const updateData: any = {};
      if (new_origin_ao_id) updateData.origin_ao_id = new_origin_ao_id;
      if (new_dest_ao_id) updateData.dest_ao_id = new_dest_ao_id;

      // Update shipment
      const updatedShipment = await prisma.shipment.update({
        where: { id },
        data: updateData
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "SHIPMENT_REASSIGNED",
        id,
        {
          old_origin_ao_id: currentShipment.origin_ao_id,
          old_dest_ao_id: currentShipment.dest_ao_id,
          new_origin_ao_id,
          new_dest_ao_id,
          reason,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "Shipment reassigned successfully",
        data: updatedShipment
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // SYSTEM SETTINGS METHODS
  // ============================================================================

  /**
   * Get system settings
   */
  static async getSystemSettings(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      const settings = await prisma.systemSettings.findFirst();

      if (!settings) {
        // Create default settings if none exist
        const defaultSettings = await prisma.systemSettings.create({
          data: {
            min_distance_km: 2.0,
            max_shipments_per_day: 10,
            max_shipments_per_user: 100,
            max_pending_shipments: 5,
            require_photo_proof: true,
            max_failed_logins: 5,
            review_period_hours: 24,
            enable_2fa: true,
            gps_tolerance_meters: 50
          }
        });

        res.status(200).json({
          success: true,
          message: "System settings retrieved successfully",
          data: defaultSettings
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "System settings retrieved successfully",
        data: settings
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update system settings
   */
  static async updateSystemSettings(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      const updateData = req.body;

      const prisma = ServiceFactory.getPrismaClient();

      // Remove updated_by from updateData if it exists (since it references User table, not Admin table)
      const { updated_by: _updated_by, ...cleanUpdateData } = updateData;

      // Get current settings
      let settings = await prisma.systemSettings.findFirst();

      if (!settings) {
        // Create new settings if none exist
        settings = await prisma.systemSettings.create({
          data: {
            ...cleanUpdateData,
            updated_by: null // Explicitly set to null since admins are not in User table
          }
        });
      } else {
        // Update existing settings
        settings = await prisma.systemSettings.update({
          where: { id: settings.id },
          data: {
            ...cleanUpdateData,
            updated_by: null // Explicitly set to null since admins are not in User table
          }
        });
      }

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "SYSTEM_SETTINGS_UPDATED",
        undefined,
        {
          changes: cleanUpdateData,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "System settings updated successfully",
        data: settings
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Reset system settings to defaults
   */
  static async resetSystemSettings(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      const prisma = ServiceFactory.getPrismaClient();

      const defaultSettings = {
        min_distance_km: 2.0,
        max_shipments_per_day: 10,
        max_shipments_per_user: 100,
        max_pending_shipments: 5,
        require_photo_proof: true,
        max_failed_logins: 5,
        review_period_hours: 24,
        enable_2fa: true,
        gps_tolerance_meters: 50,
        updated_by: null // Explicitly set to null since admins are not in User table
      };

      // Get current settings
      const currentSettings = await prisma.systemSettings.findFirst();

      let settings;
      if (!currentSettings) {
        settings = await prisma.systemSettings.create({
          data: defaultSettings
        });
      } else {
        settings = await prisma.systemSettings.update({
          where: { id: currentSettings.id },
          data: defaultSettings
        });
      }

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "SYSTEM_SETTINGS_RESET",
        undefined,
        {
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "System settings reset to defaults successfully",
        data: settings
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get settings change history
   */
  static async getSettingsHistory(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const prisma = ServiceFactory.getPrismaClient();

      // Get audit logs related to settings changes
      const [history, total] = await Promise.all([
        prisma.auditLog.findMany({
          where: {
            action: {
              in: ['SYSTEM_SETTINGS_UPDATED', 'SYSTEM_SETTINGS_RESET']
            }
          },
          skip,
          take,
          orderBy: { created_at: 'desc' },
          include: {
            admin: {
              select: { name: true, email: true }
            }
          }
        }),
        prisma.auditLog.count({
          where: {
            action: {
              in: ['SYSTEM_SETTINGS_UPDATED', 'SYSTEM_SETTINGS_RESET']
            }
          }
        })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Settings history retrieved successfully",
        data: history,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // NOTIFICATION MANAGEMENT METHODS
  // ============================================================================

  /**
   * Get all notifications
   */
  static async getAllNotifications(req: Request, res: Response): Promise<void> {
    try {
      const { page, limit, user_id, type, status, priority } = GetAllNotificationsQuerySchema.parse(req.query);

      const paginationOptions = { skip: (page - 1) * limit, take: limit };
      const paginationMeta = { page, limit };

      // Build where clause
      const where: any = {};
      if (user_id) AdminController.validateUUID(user_id, "User ID"); where.user_id = user_id;
      if (type) where.notification_type = type;
      if (status) where.read = status === 'read';
      if (priority) where.priority = priority;

      const prisma = ServiceFactory.getPrismaClient();

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          ...paginationOptions,
          orderBy: { created_at: 'desc' },
          include: {
            user: {
              select: { id: true, name: true, email: true, user_type: true }
            }
          }
        }),
        prisma.notification.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Notifications retrieved successfully",
        data: {
          notifications,
          pagination: {
            ...paginationMeta,
            total,
            totalPages,
            hasNext: (page * limit) < total,
            hasPrev: page > 1,
          }
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Broadcast notification to multiple users
   */
  static async broadcastNotification(req: Request, res: Response): Promise<void> {
    try {
      const { title, message, user_types, priority } = BroadcastNotificationBodySchema.parse(req.body);
      const adminId = req.user?.id;

      const prisma = ServiceFactory.getPrismaClient();

      // Get users to notify
      const where: any = {};
      if (user_types && user_types.length > 0) {
        where.user_type = { in: user_types };
      }

      const users = await prisma.user.findMany({
        where,
        select: { id: true }
      });

      if (users.length === 0) {
        throw AppError.badRequest("No users found matching criteria");
      }

      // Create notifications for all users
      const notificationService = ServiceFactory.getNotificationService();
      const notifications = await notificationService.createBulkNotifications(
        users.map(user => ({
          userId: user.id,
          type: 'SYSTEM' as any, // Assuming broadcast notifications are SYSTEM type
          title,
          message,
          priority: priority as any,
          metadata: {
            broadcast: true,
            sent_by_admin: adminId
          }
        }))
      );

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "BROADCAST_NOTIFICATION_SENT",
        undefined,
        {
          title,
          user_types,
          recipient_count: users.length,
          admin_id: adminId
        },
        adminId
      );

      res.status(201).json({
        success: true,
        message: `Broadcast notification sent to ${users.length} users`,
        data: {
          notification_count: notifications.length,
          recipient_count: users.length
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get notification templates (placeholder - would need template model)
   */
  static async getNotificationTemplates(req: Request, res: Response): Promise<void> {
    try {
      // This is a placeholder implementation
      // In a real system, you'd have a NotificationTemplate model
      const templates = [
        {
          id: '1',
          name: 'Welcome Template',
          title: 'Welcome to NAQALAT',
          message: 'Thank you for joining our platform',
          type: 'WELCOME'
        },
        {
          id: '2',
          name: 'Shipment Created',
          title: 'Shipment Created Successfully',
          message: 'Your shipment has been created and is awaiting pickup',
          type: 'SHIPMENT_CREATED'
        }
      ];

      res.status(200).json({
        success: true,
        message: "Notification templates retrieved successfully",
        data: templates
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Create notification template (placeholder)
   */
  static async createNotificationTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { name, title, message, type } = CreateNotificationTemplateBodySchema.parse(req.body);
      const adminId = req.user?.id;

      // Placeholder implementation
      const template = {
        id: Date.now().toString(),
        name,
        title,
        message,
        type,
        created_by: adminId,
        created_at: new Date()
      };

      res.status(201).json({
        success: true,
        message: "Notification template created successfully",
        data: template
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Update notification template (placeholder)
   */
  static async updateNotificationTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, title, message, type } = UpdateNotificationTemplateBodySchema.parse(req.body);
      const adminId = req.user?.id;

      AdminController.validateUUID(id, "template ID");

      // Placeholder implementation
      const template = {
        id,
        name,
        title,
        message,
        type,
        updated_by: adminId,
        updated_at: new Date()
      };

      res.status(200).json({
        success: true,
        message: "Notification template updated successfully",
        data: template
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      const [
        totalNotifications,
        readNotifications,
        unreadNotifications,
        notificationsByType,
        recentNotifications
      ] = await Promise.all([
        prisma.notification.count(),
        prisma.notification.count({ where: { read: true } }),
        prisma.notification.count({ where: { read: false } }),
        prisma.notification.groupBy({
          by: ['notification_type'],
          _count: { id: true }
        }),
        prisma.notification.count({
          where: {
            created_at: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        })
      ]);

      const stats = {
        total: totalNotifications,
        read: readNotifications,
        unread: unreadNotifications,
        read_rate: totalNotifications > 0 ? Math.round((readNotifications / totalNotifications) * 100) : 0,
        by_type: notificationsByType.map(item => ({
          type: item.notification_type,
          count: item._count.id
        })),
        recent_24h: recentNotifications
      };

      res.status(200).json({
        success: true,
        message: "Notification statistics retrieved successfully",
        data: stats
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // AUDIT & SECURITY METHODS
  // ============================================================================

  /**
   * Get audit logs with filtering
   */
  static async getAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const { user_id, admin_id, action, date_from, date_to } = req.query;

      // Build where clause
      const where: any = {};
      if (user_id) where.user_id = user_id;
      if (admin_id) where.admin_id = admin_id;
      if (action) where.action = { contains: action as string, mode: 'insensitive' };

      if (date_from || date_to) {
        where.created_at = {};
        if (date_from) where.created_at.gte = new Date(date_from as string);
        if (date_to) where.created_at.lte = new Date(date_to as string);
      }

      const prisma = ServiceFactory.getPrismaClient();

      const [auditLogs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip,
          take,
          orderBy: { created_at: 'desc' },
          include: {
            user: {
              select: { id: true, name: true, email: true, user_type: true }
            },
            admin: {
              select: { id: true, name: true, email: true, role: true }
            }
          }
        }),
        prisma.auditLog.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Audit logs retrieved successfully",
        data: {
          logs: auditLogs,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get login attempts
   */
  static async getLoginAttempts(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const { successful, ip_address, date_from } = req.query;

      // Build where clause
      const where: any = {};
      if (successful !== undefined) where.successful = successful === 'true';
      if (ip_address) where.ip_address = ip_address;
      if (date_from) {
        where.attempted_at = {
          gte: new Date(date_from as string)
        };
      }

      const prisma = ServiceFactory.getPrismaClient();

      const [loginAttempts, total] = await Promise.all([
        prisma.loginAttempt.findMany({
          where,
          skip,
          take,
          orderBy: { attempted_at: 'desc' },
          include: {
            user: {
              select: { id: true, name: true, email: true, user_type: true }
            },
            admin: {
              select: { id: true, name: true, email: true, role: true }
            }
          }
        }),
        prisma.loginAttempt.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Login attempts retrieved successfully",
        data: {
          loginAttempts,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get security events
   */
  static async getSecurityEvents(req: Request, res: Response): Promise<void> {
    try {
      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;
      const prisma = ServiceFactory.getPrismaClient();

      // Get security-related audit logs
      const [securityEvents, total] = await Promise.all([
        prisma.auditLog.findMany({
          where: {
            action: {
              in: [
                'USER_LOCKED',
                'USER_UNLOCKED',
                'FAILED_LOGIN_ATTEMPT',
                'SUSPICIOUS_ACTIVITY',
                'ADMIN_LOGIN',
                'ADMIN_LOGOUT'
              ]
            }
          },
          skip,
          take,
          orderBy: { created_at: 'desc' },
          include: {
            user: {
              select: { id: true, name: true, email: true, user_type: true }
            },
            admin: {
              select: { id: true, name: true, email: true, role: true }
            }
          }
        }),
        prisma.auditLog.count({
          where: {
            action: {
              in: [
                'USER_LOCKED',
                'USER_UNLOCKED',
                'FAILED_LOGIN_ATTEMPT',
                'SUSPICIOUS_ACTIVITY',
                'ADMIN_LOGIN',
                'ADMIN_LOGOUT'
              ]
            }
          }
        })
      ]);

      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: "Security events retrieved successfully",
        data: securityEvents,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Lock user account
   */
  static async lockUser(req: Request, res: Response): Promise<void> {
    try {
      const { user_id, reason } = req.body;
      const adminId = req.user?.id;

      if (!user_id) {
        throw AppError.badRequest("user_id is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Update user status to suspended
      const updatedUser = await prisma.user.update({
        where: { id: user_id },
        data: {
          status: UserStatus.SUSPENDED,
          updated_by: adminId
        }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "USER_LOCKED_BY_ADMIN",
        undefined,
        {
          locked_user_id: user_id,
          reason,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "User account locked successfully",
        data: updatedUser
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Unlock user account
   */
  static async unlockUser(req: Request, res: Response): Promise<void> {
    try {
      const { user_id, reason } = req.body;
      const adminId = req.user?.id;

      if (!user_id) {
        throw AppError.badRequest("user_id is required");
      }

      const prisma = ServiceFactory.getPrismaClient();

      // Update user status to active
      const updatedUser = await prisma.user.update({
        where: { id: user_id },
        data: {
          status: UserStatus.ACTIVE,
          updated_by: adminId
        }
      });

      // Create audit log
      const auditLogService = ServiceFactory.getAuditLogService();
      await auditLogService.createAuditLog(
        undefined,
        "USER_UNLOCKED_BY_ADMIN",
        undefined,
        {
          unlocked_user_id: user_id,
          reason,
          admin_id: adminId
        },
        adminId
      );

      res.status(200).json({
        success: true,
        message: "User account unlocked successfully",
        data: updatedUser
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // REPORTING & EXPORT METHODS
  // ============================================================================

  /**
   * Get user reports
   */
  static async getUserReports(req: Request, res: Response): Promise<void> {
    try {
      const { format = 'json', date_from, date_to, user_type } = req.query;
      const prisma = ServiceFactory.getPrismaClient();

      // Build where clause for date filtering
      const where: any = {};
      if (user_type) where.user_type = user_type;
      if (date_from || date_to) {
        where.created_at = {};
        if (date_from) where.created_at.gte = new Date(date_from as string);
        if (date_to) where.created_at.lte = new Date(date_to as string);
      }

      const [
        userStats,
        usersByType,
        usersByStatus,
        recentRegistrations
      ] = await Promise.all([
        prisma.user.count({ where }),
        prisma.user.groupBy({
          by: ['user_type'],
          where,
          _count: { id: true }
        }),
        prisma.user.groupBy({
          by: ['status'],
          where,
          _count: { id: true }
        }),
        prisma.user.findMany({
          where: {
            ...where,
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          },
          select: {
            id: true,
            name: true,
            email: true,
            user_type: true,
            status: true,
            created_at: true
          },
          orderBy: { created_at: 'desc' }
        })
      ]);

      const report = {
        summary: {
          total_users: userStats,
          by_type: usersByType.map(item => ({
            type: item.user_type,
            count: item._count.id
          })),
          by_status: usersByStatus.map(item => ({
            status: item.status,
            count: item._count.id
          }))
        },
        recent_registrations: recentRegistrations,
        generated_at: new Date().toISOString(),
        filters: { user_type, date_from, date_to }
      };

      if (format === 'json') {
        res.status(200).json({
          success: true,
          message: "User report generated successfully",
          data: report
        });
      } else {
        // For CSV/Excel formats, you would implement export logic here
        res.status(200).json({
          success: true,
          message: `User report in ${format} format not yet implemented`,
          data: { export_url: `/api/admin/export/users?format=${format}` }
        });
      }
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get shipment reports
   */
  static async getShipmentReports(req: Request, res: Response): Promise<void> {
    try {
      const { status, date_from, date_to } = req.query;
      const prisma = ServiceFactory.getPrismaClient();

      // Build where clause
      const where: any = {};
      if (status) where.status = status;
      if (date_from || date_to) {
        where.created_at = {};
        if (date_from) where.created_at.gte = new Date(date_from as string);
        if (date_to) where.created_at.lte = new Date(date_to as string);
      }

      const [
        shipmentStats,
        shipmentsByStatus,
        deliveryStats,
        recentShipments
      ] = await Promise.all([
        prisma.shipment.count({ where }),
        prisma.shipment.groupBy({
          by: ['status'],
          where,
          _count: { id: true }
        }),
        prisma.shipment.count({
          where: { ...where, status: ShipmentStatus.DELIVERED }
        }),
        prisma.shipment.findMany({
          where: {
            ...where,
            created_at: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          },
          select: {
            id: true,
            tracking_code: true,
            status: true,
            created_at: true,
            customer: { select: { name: true } },
            originAO: { select: { business_name: true } },
            destAO: { select: { business_name: true } }
          },
          orderBy: { created_at: 'desc' },
          take: 50
        })
      ]);

      const report = {
        summary: {
          total_shipments: shipmentStats,
          delivered_shipments: deliveryStats,
          delivery_rate: shipmentStats > 0 ? Math.round((deliveryStats / shipmentStats) * 100) : 0,
          by_status: shipmentsByStatus.map(item => ({
            status: item.status,
            count: item._count.id
          }))
        },
        recent_shipments: recentShipments,
        generated_at: new Date().toISOString(),
        filters: { status, date_from, date_to }
      };

      res.status(200).json({
        success: true,
        message: "Shipment report generated successfully",
        data: report
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get system reports
   */
  static async getSystemReports(req: Request, res: Response): Promise<void> {
    try {
      const prisma = ServiceFactory.getPrismaClient();

      const [
        systemStats,
        performanceMetrics,
        errorLogs
      ] = await Promise.all([
        // System statistics
        Promise.all([
          prisma.user.count(),
          prisma.shipment.count(),
          prisma.admin.count(),
          prisma.notification.count()
        ]).then(([users, shipments, admins, notifications]) => ({
          users,
          shipments,
          admins,
          notifications
        })),

        // Performance metrics (placeholder)
        Promise.resolve({
          uptime_hours: Math.floor(process.uptime() / 3600),
          memory_usage_mb: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          avg_response_time_ms: 150 // Placeholder
        }),

        // Recent error logs from audit logs
        prisma.auditLog.findMany({
          where: {
            action: { contains: 'ERROR' }
          },
          take: 10,
          orderBy: { created_at: 'desc' }
        })
      ]);

      const report = {
        system_statistics: systemStats,
        performance_metrics: performanceMetrics,
        recent_errors: errorLogs,
        generated_at: new Date().toISOString()
      };

      res.status(200).json({
        success: true,
        message: "System report generated successfully",
        data: report
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Export users data to CSV/Excel
   */
  static async exportUsers(req: Request, res: Response): Promise<void> {
    try {
      const { format = 'csv', filters = {} } = req.body;

      const prisma = ServiceFactory.getPrismaClient();
      
      // Build where clause based on filters
      const where: any = {};
      
      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { email: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      
      if (filters.user_type) {
        where.user_type = filters.user_type;
      }
      
      if (filters.status) {
        where.status = filters.status;
      }

      // Fetch all users for export (no pagination)
      const users = await prisma.user.findMany({
        where,
        orderBy: { created_at: 'desc' },
        include: {
          accessOperator: {
            select: {
              business_name: true,
              address: true,
              approved: true
            }
          },
          carOperator: {
            select: {
              license_number: true,
              vehicle_info: true,
              approved: true
            }
          },
          _count: {
            select: {
              shipments: true
            }
          }
        }
      });

      if (format.toLowerCase() === 'csv') {
        // Generate CSV content
        const csvHeaders = [
          'ID', 'Name', 'Email', 'Phone', 'User Type', 'Status',
          'Email Verified', 'Business Name', 'License Number', 'Vehicle Info', 
          'Operator Approved', 'Total Shipments', 'Created At', 'Updated At'
        ];
        
        const csvRows = users.map(user => [
          user.id,
          user.name || '',
          user.email,
          user.phone || '',
          user.user_type,
          user.status,
          user.email_verified ? 'Yes' : 'No',
          user.accessOperator?.business_name || '',
          user.carOperator?.license_number || '',
          user.carOperator?.vehicle_info || '',
          (user.accessOperator?.approved || user.carOperator?.approved) ? 'Yes' : 'No',
          user._count.shipments,
          user.created_at.toISOString(),
          user.updated_at.toISOString()
        ]);

        const csvContent = [
          csvHeaders.join(','),
          ...csvRows.map(row => row.map(field => 
            typeof field === 'string' && (field.includes(',') || field.includes('"')) 
              ? `"${field.replace(/"/g, '""')}"` 
              : field
          ).join(','))
        ].join('\n');

        // Send CSV file
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="users_export_${new Date().toISOString().split('T')[0]}.csv"`);
        res.send(csvContent);
        
      } else {
        // For other formats, return JSON for now
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="users_export_${new Date().toISOString().split('T')[0]}.json"`);
        res.json({
          success: true,
          message: `Users exported successfully in ${format} format`,
          data: {
            total_records: users.length,
            export_date: new Date().toISOString(),
            users
          }
        });
      }
      
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Export shipments data to CSV/Excel
   */
  static async exportShipments(req: Request, res: Response): Promise<void> {
    try {
      const { format = 'csv', filters = {} } = req.body;

      const prisma = ServiceFactory.getPrismaClient();
      
      // Build where clause based on filters
      const where: any = {};
      
      if (filters.search) {
        where.OR = [
          { tracking_code: { contains: filters.search, mode: 'insensitive' } },
          { sender_name: { contains: filters.search, mode: 'insensitive' } },
          { receiver_name: { contains: filters.search, mode: 'insensitive' } }
        ];
      }
      
      if (filters.status) {
        where.status = filters.status;
      }
      
      if (filters.date_from) {
        where.created_at = { gte: new Date(filters.date_from) };
      }
      
      if (filters.date_to) {
        where.created_at = { 
          ...where.created_at,
          lte: new Date(filters.date_to) 
        };
      }

      // Fetch all shipments for export
      const shipments = await prisma.shipment.findMany({
        where,
        orderBy: { created_at: 'desc' },
        include: {
          customer: {
            select: {
              name: true,
              email: true,
              phone: true
            }
          },
          qrLabels: {
            select: {
              qr_value: true,
              assigned_at: true
            }
          }
        }
      });

      if (format.toLowerCase() === 'csv') {
        // Generate CSV content
        const csvHeaders = [
          'Tracking Code', 'Receiver Name', 'Receiver Phone', 'Status', 'Weight', 'Size', 'Description',
          'Customer Name', 'Customer Email', 'QR Code', 'Pickup Code', 'Created At', 'Updated At'
        ];
        
        const csvRows = shipments.map(shipment => [
          shipment.tracking_code,
          shipment.receiver_name || '',
          shipment.receiver_phone || '',
          shipment.status,
          shipment.weight || '',
          shipment.size || '',
          shipment.description || '',
          shipment.customer?.name || '',
          shipment.customer?.email || '',
          shipment.qrLabels?.[0]?.qr_value || '',
          shipment.pickup_code || '',
          shipment.created_at.toISOString(),
          shipment.updated_at.toISOString()
        ]);

        const csvContent = [
          csvHeaders.join(','),
          ...csvRows.map(row => row.map(field => 
            typeof field === 'string' && (field.includes(',') || field.includes('"')) 
              ? `"${field.replace(/"/g, '""')}"` 
              : field
          ).join(','))
        ].join('\n');

        // Send CSV file
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="shipments_export_${new Date().toISOString().split('T')[0]}.csv"`);
        res.send(csvContent);
        
      } else {
        // For other formats, return JSON for now
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="shipments_export_${new Date().toISOString().split('T')[0]}.json"`);
        res.json({
          success: true,
          message: `Shipments exported successfully in ${format} format`,
          data: {
            total_records: shipments.length,
            export_date: new Date().toISOString(),
            shipments
          }
        });
      }
      
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Download export file (deprecated - exports now return files directly)
   */
  static async downloadExport(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      AdminController.validateUUID(id, "export ID");

      res.status(200).json({
        success: false,
        message: "Export download is deprecated. Exports now return files directly via POST /export/users or POST /export/shipments endpoints.",
        data: { 
          export_id: id,
          instructions: "Use POST /api/admin/export/users or POST /api/admin/export/shipments with format parameter to get instant downloads"
        }
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  // ============================================================================
  // ADMIN NOTIFICATION MANAGEMENT
  // ============================================================================

  /**
   * Get admin's own notifications
   */
  static async getMyAdminNotifications(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      if (!adminId) {
        throw AppError.unauthorized("Admin authentication required");
      }

      const { paginationOptions, paginationMeta } = parsePaginationParams(req.query);
      const { page, limit } = paginationMeta;
      const { skip } = paginationOptions;

      // Parse filters
      const { type, priority, read, search, from_date, to_date, shipment_id } = req.query;

      const filters: any = {};
      if (type) filters.type = type as string;
      if (priority) filters.priority = priority as string;
      if (read !== undefined) filters.read = read === 'true';
      if (search) filters.search = search as string;
      if (from_date) filters.fromDate = new Date(from_date as string);
      if (to_date) filters.toDate = new Date(to_date as string);
      if (shipment_id) filters.shipmentId = shipment_id as string;

      const adminNotificationService = ServiceFactory.getAdminNotificationService();

      // Get notifications and counts
      const [notifications, totalCount, unreadCount] = await Promise.all([
        adminNotificationService.getAdminNotificationsByAdminId(adminId, filters, {
          skip,
          take: limit,
        }),
        adminNotificationService.getAdminNotificationCount(adminId, filters),
        adminNotificationService.getAdminNotificationCount(adminId, { read: false }),
      ]);

      res.status(200).json({
        success: true,
        data: {
          notifications,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit),
            hasNext: (page + 1) * limit < totalCount,
            hasPrev: page > 0,
          },
          unreadCount,
        },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Get admin's unread notification count
   */
  static async getAdminUnreadCount(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      if (!adminId) {
        throw AppError.unauthorized("Admin authentication required");
      }

      const adminNotificationService = ServiceFactory.getAdminNotificationService();
      const unreadCount = await adminNotificationService.getAdminNotificationCount(
        adminId,
        { read: false }
      );

      res.status(200).json({
        success: true,
        data: {
          unreadCount,
        },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Mark admin notification as read
   */
  static async markAdminNotificationAsRead(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      if (!adminId) {
        throw AppError.unauthorized("Admin authentication required");
      }

      const { id } = req.params;
      AdminController.validateUUID(id, "notification ID");

      const adminNotificationService = ServiceFactory.getAdminNotificationService();
      const notification = await adminNotificationService.markAdminNotificationAsRead(id, adminId);

      res.status(200).json({
        success: true,
        message: "Notification marked as read successfully",
        data: { notification },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Mark all admin notifications as read
   */
  static async markAllAdminNotificationsAsRead(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      if (!adminId) {
        throw AppError.unauthorized("Admin authentication required");
      }

      const adminNotificationService = ServiceFactory.getAdminNotificationService();
      const updatedCount = await adminNotificationService.markAllAdminNotificationsAsRead(adminId);

      res.status(200).json({
        success: true,
        message: `Marked ${updatedCount} notifications as read`,
        data: { updatedCount },
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

  /**
   * Delete admin notification
   */
  static async deleteAdminNotification(req: Request, res: Response): Promise<void> {
    try {
      const adminId = req.user?.id;
      if (!adminId) {
        throw AppError.unauthorized("Admin authentication required");
      }

      const { id } = req.params;
      AdminController.validateUUID(id, "notification ID");

      const adminNotificationService = ServiceFactory.getAdminNotificationService();
      await adminNotificationService.deleteAdminNotification(id, adminId);

      res.status(200).json({
        success: true,
        message: "Notification deleted successfully",
      });
    } catch (error) {
      AppError.handleError(error, req, res);
    }
  }

}
