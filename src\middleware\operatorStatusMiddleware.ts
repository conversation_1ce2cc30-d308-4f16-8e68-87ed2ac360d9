import { Request, Response, NextFunction } from 'express';

import { ServiceFactory } from '../services/ServiceFactory';
import { UserType, UserStatus } from '../types/models';

/**
 * Middleware to ensure Access Operators and Car Operators are both ACTIVE and APPROVED
 * before allowing access to operational routes.
 * 
 * This middleware:
 * - Allows CUSTOMER and ADMIN users if they are ACTIVE (no approval check needed)
 * - Requires ACCESS_OPERATOR and CAR_OPERATOR users to be both ACTIVE and APPROVED
 * - Should be used on operational routes but NOT on profile management routes
 */
export const requireActiveAndApprovedOperator = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated (should be handled by previous middleware)
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: {
          type: 'AUTHENTICATION_ERROR',
          details: 'User not authenticated',
        },
      });
      return;
    }

    const { id: userId, user_type: userType } = req.user;

    if (!userId || !userType) {
      res.status(401).json({
        success: false,
        message: 'Invalid authentication token',
        error: {
          type: 'AUTHENTICATION_ERROR',
          details: 'Token missing required user information',
        },
      });
      return;
    }

    const prisma = ServiceFactory.getPrismaClient();

    // Get user with their operator data if applicable
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        accessOperator: userType === UserType.ACCESS_OPERATOR,
        carOperator: userType === UserType.CAR_OPERATOR,
      },
    });

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
        error: {
          type: 'NOT_FOUND_ERROR',
          details: 'User account could not be found',
        },
      });
      return;
    }

    // Check if user is active
    if (user.status !== UserStatus.ACTIVE) {
      res.status(403).json({
        success: false,
        message: 'Account is not active',
        error: {
          type: 'AUTHORIZATION_ERROR',
          details: 'Your account is pending approval, suspended, or inactive. Please contact support.',
        },
      });
      return;
    }

    // For operators, also check approval status
    if (userType === UserType.ACCESS_OPERATOR) {
      if (!user.accessOperator) {
        res.status(404).json({
          success: false,
          message: 'Access Operator profile not found',
          error: {
            type: 'NOT_FOUND_ERROR',
            details: 'Access Operator profile could not be found',
          },
        });
        return;
      }

      if (!user.accessOperator.approved) {
        res.status(403).json({
          success: false,
          message: 'Access Operator account pending approval',
          error: {
            type: 'AUTHORIZATION_ERROR',
            details: 'Your Access Operator account is pending admin approval. You can update your profile but cannot perform operational tasks until approved.',
          },
        });
        return;
      }
    } else if (userType === UserType.CAR_OPERATOR) {
      if (!user.carOperator) {
        res.status(404).json({
          success: false,
          message: 'Car Operator profile not found',
          error: {
            type: 'NOT_FOUND_ERROR',
            details: 'Car Operator profile could not be found',
          },
        });
        return;
      }

      if (!user.carOperator.approved) {
        res.status(403).json({
          success: false,
          message: 'Car Operator account pending approval',
          error: {
            type: 'AUTHORIZATION_ERROR',
            details: 'Your Car Operator account is pending admin approval. You can update your profile but cannot perform operational tasks until approved.',
          },
        });
        return;
      }
    }
    // For CUSTOMER and ADMIN users, only the ACTIVE status check above is needed

    // User is active and (if operator) approved - proceed
    next();
  } catch (error) {
    console.error('Operator status middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: {
        type: 'INTERNAL_ERROR',
        details: 'An error occurred while checking user status',
      },
    });
  }
};
