/* eslint-disable no-console */
import { PrismaClient } from '@prisma/client';

import { ServiceFactory } from './ServiceFactory';
import { CancellationReason, ShipmentStatus } from '../types/models';

export class ScheduledJobService {
  private prisma: PrismaClient;
  // eslint-disable-next-line no-undef
  private intervalId: NodeJS.Timeout | null = null;
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 5000; // 5 seconds

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Retry database operations with exponential backoff for VPN connection issues
   */
  private async retryDatabaseOperation<T>(
    operation: () => Promise<T>,
    retries: number = this.MAX_RETRIES
  ): Promise<T> {
    try {
      return await operation();
    } catch (error: any) {
      if (retries > 0 && this.isConnectionError(error)) {
        console.log(`🔄 Database connection failed, retrying in ${this.RETRY_DELAY}ms... (${this.MAX_RETRIES - retries + 1}/${this.MAX_RETRIES})`);
        await this.delay(this.RETRY_DELAY * (this.MAX_RETRIES - retries + 1)); // Exponential backoff
        return this.retryDatabaseOperation(operation, retries - 1);
      }
      throw error;
    }
  }

  /**
   * Check if error is a connection-related error
   */
  private isConnectionError(error: any): boolean {
    const connectionErrors = [
      'connection pool timeout',
      'connection timeout',
      'timed out',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'connection terminated',
      'connection lost'
    ];

    const errorMessage = error?.message?.toLowerCase() || '';
    return connectionErrors.some(errorType => errorMessage.includes(errorType));
  }

  /**
   * Delay utility for retry mechanism
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Test database connection health
   */
  private async testDatabaseConnection(): Promise<boolean> {
    try {
      await this.retryDatabaseOperation(async () => {
        await this.prisma.$queryRaw`SELECT 1`;
      });
      return true;
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      return false;
    }
  }

  /**
   * Start the scheduled job to check for expired shipments
   * Runs every 5 minutes
   */
  startExpiredShipmentChecker(): void {
    console.log('🕐 Starting expired shipment checker...');

    // Test connection first
    this.testDatabaseConnection().then(isConnected => {
      // eslint-disable-next-line promise/always-return
      if (isConnected) {
        console.log('✅ Database connection test passed');
        // Run immediately on start
        this.checkExpiredShipments();
      } else {
        console.log('⚠️  Database connection test failed, will retry on next interval');
      }
    }).catch(error => {
      console.error('❌ Error during database connection test:', error);
    });

    // Then run every 5 minutes (300,000 ms)
    this.intervalId = setInterval(() => {
      this.checkExpiredShipments();
    }, 5 * 60 * 1000);
  }

  /**
   * Stop the scheduled job
   */
  stopExpiredShipmentChecker(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('🛑 Stopped expired shipment checker');
    }
  }

  /**
   * Check for expired shipments and cancel them
   */
  private async checkExpiredShipments(): Promise<void> {
    try {
      const now = new Date();

      // Find all PENDING shipments that have expired with retry mechanism
      const expiredShipments = await this.retryDatabaseOperation(async () => {
        return await this.prisma.shipment.findMany({
          where: {
            status: ShipmentStatus.PENDING,
            expires_at: {
              lte: now
            }
          },
          include: {
            customer: true
          }
        });
      });

      if (expiredShipments.length === 0) {
        console.log('✅ No expired shipments found');
        return;
      }

      console.log(`⚠️  Found ${expiredShipments.length} expired shipment(s)`);

      // Cancel each expired shipment
      for (const shipment of expiredShipments) {
        await this.cancelExpiredShipment(shipment.id, shipment.customer_id);
      }

      console.log(`✅ Successfully cancelled ${expiredShipments.length} expired shipment(s)`);
    } catch (error: any) {
      if (this.isConnectionError(error)) {
        console.error('❌ Database connection error while checking expired shipments (VPN issue?):', error?.message);
        console.log('🔄 Will retry on next scheduled check...');
      } else {
        console.error('❌ Error checking expired shipments:', error);
      }
    }
  }

  /**
   * Cancel an expired shipment with proper logging and notifications
   */
  private async cancelExpiredShipment(shipmentId: string, customerId: string): Promise<void> {
    try {
      const shipmentStatusService = ServiceFactory.getShipmentStatusService();
      const emailService = ServiceFactory.getEmailService();

      // Get customer details for email notification
      const customer = await this.prisma.user.findUnique({
        where: { id: customerId }
      });

      // Update shipment status with system cancellation
      await shipmentStatusService.updateShipmentStatusWithReason(
        shipmentId,
        ShipmentStatus.CANCELLED,
        null, // No user ID for system cancellation
        CancellationReason.SYSTEM_EXPIRED,
        'Shipment automatically cancelled due to 24-hour expiry'
      );

      // Send email notification to customer
      if (customer) {
        try {
          await emailService.sendShipmentExpiredEmail(
            customer.email,
            customer.name,
            shipmentId
          );
          console.log(`📧 Sent expiry notification email to: ${customer.email}`);
        } catch (emailError) {
          console.error(`❌ Failed to send expiry email for shipment ${shipmentId}:`, emailError);
        }
      }

      console.log(`📦 Cancelled expired shipment: ${shipmentId}`);
    } catch (error) {
      console.error(`❌ Error cancelling expired shipment ${shipmentId}:`, error);
    }
  }

  /**
   * Get statistics about expired shipments
   */
  async getExpiredShipmentStats(): Promise<{
    totalExpired: number;
    expiredToday: number;
    expiredThisWeek: number;
  }> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [totalExpired, expiredToday, expiredThisWeek] = await Promise.all([
      this.prisma.shipment.count({
        where: {
          status: ShipmentStatus.CANCELLED,
          cancellation_reason: CancellationReason.SYSTEM_EXPIRED
        }
      }),
      this.prisma.shipment.count({
        where: {
          status: ShipmentStatus.CANCELLED,
          cancellation_reason: CancellationReason.SYSTEM_EXPIRED,
          cancelled_at: {
            gte: todayStart
          }
        }
      }),
      this.prisma.shipment.count({
        where: {
          status: ShipmentStatus.CANCELLED,
          cancellation_reason: CancellationReason.SYSTEM_EXPIRED,
          cancelled_at: {
            gte: weekStart
          }
        }
      })
    ]);

    return {
      totalExpired,
      expiredToday,
      expiredThisWeek
    };
  }
}
