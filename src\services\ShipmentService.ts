import { PrismaClient } from "@prisma/client";

import { BaseService } from "./BaseService";
import { ServiceFactory } from "./ServiceFactory";
import { ShipmentNotificationService } from "./ShipmentNotificationService";
import { ShipmentStatusService } from "./ShipmentStatusService";
import {
  IShipmentService,
  IAuditLogService,
  INotificationService,
} from "../interfaces/services";
import {
  Shipment,
  ShipmentStatus,
  QRLabelStatus,
  CancellationReason,
} from "../types/models";
import { generateRandomCode } from "../utils/codeGenerator";
import { AppError } from "../utils/errors";

function isUUID(str: string): boolean {
  return /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
    str
  );
}

export class ShipmentService extends BaseService implements IShipmentService {
  private auditLogService: IAuditLogService;
  private notificationService: INotificationService;
  private shipmentStatusService: ShipmentStatusService;
  private shipmentNotificationService: ShipmentNotificationService;

  constructor(
    prisma: PrismaClient,
    auditLogService: IAuditLogService,
    notificationService: INotificationService,
    shipmentStatusService?: ShipmentStatusService,
    shipmentNotificationService?: ShipmentNotificationService
  ) {
    super(prisma);
    this.auditLogService = auditLogService;
    this.notificationService = notificationService;

    // If shipmentStatusService is provided, use it; otherwise, create a new instance
    this.shipmentStatusService =
      shipmentStatusService ||
      new ShipmentStatusService(prisma, auditLogService, notificationService);

    // Initialize shipment notification service (will be set later by ServiceFactory)
    this.shipmentNotificationService = shipmentNotificationService as any;
  }

  async createShipment(
    shipmentData: Omit<
      Shipment,
      | "id"
      | "created_at"
      | "updated_at"
      | "status"
      | "cancellation_reason"
      | "cancelled_at"
      | "expires_at"
    >
  ): Promise<Shipment> {
    // Validate that there's a Car Operator available for this route
    await this.validateRouteAvailability(
      shipmentData.origin_ao_id!,
      shipmentData.dest_ao_id!
    );

    // Generate a pickup code
    const pickupCode = generateRandomCode(6);

    // Set expiry date to 24 hours from now
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Create the shipment
    const shipment = await this.prisma.shipment.create({
      data: {
        customer_id: shipmentData.customer_id,
        origin_ao_id: shipmentData.origin_ao_id,
        dest_ao_id: shipmentData.dest_ao_id,
        weight: shipmentData.weight,
        size: shipmentData.size,
        description: shipmentData.description,
        receiver_name: shipmentData.receiver_name,
        receiver_phone: shipmentData.receiver_phone,
        status: ShipmentStatus.PENDING,
        pickup_code: pickupCode,
        expires_at: expiresAt,
        tracking_code: generateRandomCode(8), // Generate tracking code
      },
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      shipmentData.customer_id,
      "SHIPMENT_CREATED",
      shipment.id,
      {
        origin_ao_id: shipmentData.origin_ao_id,
        dest_ao_id: shipmentData.dest_ao_id,
        customer_qr_code: pickupCode, // Customer QR Code for drop-off verification
      }
    );

    // Notify admins about new shipment creation (optional - can be disabled if too frequent)
    try {
      const adminNotificationService = ServiceFactory.getAdminNotificationService();
      await adminNotificationService.notifyAllAdmins(
        "SHIPMENT_CREATED" as any,
        "New Shipment Created",
        `New shipment created with tracking code ${shipment.tracking_code}`,
        "LOW" as any,
        {
          shipment_id: shipment.id,
          tracking_code: shipment.tracking_code,
          customer_id: shipmentData.customer_id,
          origin_ao_id: shipmentData.origin_ao_id,
          dest_ao_id: shipmentData.dest_ao_id,
          weight: shipmentData.weight,
          size: shipmentData.size
        },
        shipment.id
      );
    } catch (error) {
      console.error("Failed to notify admins about new shipment:", error);
      // Don't throw error to avoid breaking shipment creation
    }

    // Send comprehensive notifications to all stakeholders
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(
            shipment.id
          );
        if (context) {
          await this.shipmentNotificationService.notifyShipmentCreated(context);
        }
      } else {
        // Fallback to basic notification
        await this.notificationService.createNotification(
          shipmentData.customer_id,
          shipment.id,
          "SHIPMENT_CREATED",
          `Your shipment has been created. Show your QR code (pickup code: ${pickupCode}) to the origin access point for drop-off.`
        );
      }
    } catch (error) {
      console.error(
        "❌ Failed to send shipment creation notifications:",
        error
      );
      // Don't throw error to avoid breaking shipment creation
    }

    return {
      ...shipment,
      shipmentIdQR: shipment.id, // Shipment ID QR for drop-off linking
      pickupCodeQR: pickupCode, // Pickup Code QR for final delivery
    } as Shipment & { shipmentIdQR: string; pickupCodeQR: string };
  }

  async getShipmentById(id: string): Promise<Shipment | null> {
    const shipment = await this.prisma.shipment.findUnique({
      where: { id },
      include: {
        photos: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                user_type: true,
              },
            },
          },
          orderBy: {
            created_at: "asc",
          },
        },
      },
    });

    return shipment as Shipment | null;
  }

  async updateShipment(
    id: string,
    shipmentData: Partial<Shipment>,
    updatedBy?: string
  ): Promise<Shipment> {
    // Verify the shipment exists
    const existingShipment = await this.getShipmentById(id);
    if (!existingShipment) {
      throw new Error(`Shipment with ID ${id} not found`);
    }

    // Update the shipment with updatedBy tracking
    const updateData: any = { ...shipmentData };
    if (updatedBy) {
      updateData.updated_by = updatedBy;
    }

    const shipment = await this.prisma.shipment.update({
      where: { id },
      data: updateData,
    });

    // Log the action
    await this.auditLogService.createAuditLog(
      existingShipment.customer_id,
      "SHIPMENT_UPDATED",
      id,
      { updated_fields: Object.keys(shipmentData) }
    );

    return shipment as Shipment;
  }

  async updateShipmentStatus(
    id: string,
    status: ShipmentStatus
  ): Promise<Shipment> {
    // Verify the shipment exists
    const existingShipment = await this.getShipmentById(id);
    if (!existingShipment) {
      throw new Error(`Shipment with ID ${id} not found`);
    }

    // Use the ShipmentStatusService to handle the status change with all required logging
    const result = await this.shipmentStatusService.updateShipmentStatus(
      id,
      status,
      existingShipment.customer_id,
      "Status updated via ShipmentService"
    );

    // Convert the result to match the Shipment interface
    return result as unknown as Shipment;
  }

  async getShipmentsByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number; stats?: any }> {
    const { skip, take, search, status, sort, originAoId, destAoId } =
      options || {};

    console.info("Service getShipmentsByCustomerId called with:", {
      customerId,
      options,
    });

    // Build base where clause
    const baseConditions: any[] = [{ customer_id: customerId }];

    // Add status filter
    if (status && status !== "") {
      // Ensure status is uppercase to match enum values
      const normalizedStatus = status.toUpperCase();
      baseConditions.push({ status: normalizedStatus });
    }

    // Add origin AO filter
    if (originAoId && originAoId !== "") {
      baseConditions.push({ origin_ao_id: originAoId });
    }

    // Add destination AO filter
    if (destAoId && destAoId !== "") {
      baseConditions.push({ dest_ao_id: destAoId });
    }

    // Build where clause
    let where: any;

    if (search && search.trim() !== "") {
      const searchFloat = parseFloat(search);
      const searchDate = isNaN(Date.parse(search))
        ? undefined
        : new Date(search);
      const isSearchUUID = isUUID(search);

      // Check if search term is a valid enum value
      const isValidStatus = Object.values(ShipmentStatus).includes(
        search.toUpperCase() as ShipmentStatus
      );
      const isValidCancellationReason = Object.values(
        CancellationReason
      ).includes(search.toUpperCase() as CancellationReason);

      // Only use contains for string fields, equals for others
      const orConditions = [
        isSearchUUID ? { id: { equals: search } } : undefined,
        isSearchUUID ? { customer_id: { equals: search } } : undefined,
        isSearchUUID ? { origin_ao_id: { equals: search } } : undefined,
        isSearchUUID ? { dest_ao_id: { equals: search } } : undefined,
        isSearchUUID
          ? { assigned_car_operator_id: { equals: search } }
          : undefined,
        isValidStatus
          ? { status: { equals: search.toUpperCase() } }
          : undefined,
        !isNaN(searchFloat) ? { weight: { equals: searchFloat } } : undefined,
        { size: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { pickup_code: { contains: search, mode: "insensitive" } },
        { tracking_code: { contains: search, mode: "insensitive" } },
        { receiver_name: { contains: search, mode: "insensitive" } },
        { receiver_phone: { contains: search, mode: "insensitive" } },
        searchDate ? { estimated_delivery: { equals: searchDate } } : undefined,
        searchDate ? { picked_up_at: { equals: searchDate } } : undefined,
        isValidCancellationReason
          ? { cancellation_reason: { equals: search.toUpperCase() } }
          : undefined,
        searchDate ? { cancelled_at: { equals: searchDate } } : undefined,
        searchDate ? { expires_at: { equals: searchDate } } : undefined,
        searchDate ? { created_at: { equals: searchDate } } : undefined,
        searchDate ? { updated_at: { equals: searchDate } } : undefined,
        isSearchUUID ? { updated_by: { equals: search } } : undefined, // UUID field should use equals, not contains
      ].filter((cond) => cond && Object.values(cond)[0] !== undefined);
      where = {
        AND: [
          ...baseConditions,
          {
            OR: orConditions,
          },
        ],
      };
    } else {
      // If no search, just use base conditions
      where =
        baseConditions.length === 1
          ? baseConditions[0]
          : { AND: baseConditions };
    }

    // Build orderBy clause
    let orderBy: any = { created_at: "desc" }; // default
    if (sort) {
      const [field, direction] = sort.split(":");
      const sortField = this.mapSortField(field);
      orderBy = { [sortField]: direction === "asc" ? "asc" : "desc" };
    }

    console.info("Generated where clause:", JSON.stringify(where, null, 2));
    console.info("Generated orderBy clause:", JSON.stringify(orderBy, null, 2));

    // Get total count for filtered results
    const total = await this.prisma.shipment.count({ where });
    console.info("Total count:", total);

    // Get shipments with pagination and filtering
    const shipments = await this.prisma.shipment.findMany({
      where,
      orderBy,
      skip,
      take,
    });
    console.info("Retrieved shipments count:", shipments.length);

    // Calculate stats for the customer (all shipments, not just filtered)
    const statsWhere = { customer_id: customerId };
    const stats = await this.calculateShipmentStats(statsWhere);

    return { shipments: shipments as Shipment[], total, stats };
  }

  async getShipmentsByAccessOperatorId(
    aoId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number; stats?: any }> {
    const { skip, take, search, status, sort, originAoId, destAoId } =
      options || {};

    // Check if access operator profile exists (optional check for better UX)
    const accessOperator = await this.prisma.accessOperator.findUnique({
      where: { id: aoId },
      select: { approved: true },
    });

    // If no profile exists, return empty results with message
    if (!accessOperator) {
      console.warn(`Access operator profile not found for user ID: ${aoId}`);
      return {
        shipments: [],
        total: 0,
        // message: 'Please complete your business profile to view shipments.'
      };
    }

    // If not approved, still show shipments but with a message
    if (!accessOperator.approved) {
      console.info(`Access operator ${aoId} is not yet approved`);
    }

    // Build base where clause - AO can see shipments where they are origin or destination
    let baseWhere: any = { OR: [
      { origin_ao_id: aoId },
      { dest_ao_id: aoId },
    ]};

    // Apply additional AO filters if specified
    if (originAoId && originAoId !== "") {
      // Filter to only show shipments from specific origin AO
      baseWhere = {
        AND: [baseWhere, { origin_ao_id: originAoId }],
      };
    }

    if (destAoId && destAoId !== "") {
      // Filter to only show shipments to specific destination AO
      baseWhere = {
        AND: [baseWhere, { dest_ao_id: destAoId }],
      };
    }

    let where: any = baseWhere;

    // Add status filter
    if (status && status !== "") {
      // Ensure status is uppercase to match enum values
      const normalizedStatus = status.toUpperCase();
      where = {
        AND: [baseWhere, { status: normalizedStatus }],
      };
    }

    // Add search filter
    if (search && search.trim() !== "") {
      const searchFloat = parseFloat(search);
      const searchDate = isNaN(Date.parse(search))
        ? undefined
        : new Date(search);
      const isSearchUUID = isUUID(search);

      // Check if search term is a valid enum value
      const isValidStatus = Object.values(ShipmentStatus).includes(
        search.toUpperCase() as ShipmentStatus
      );
      const isValidCancellationReason = Object.values(
        CancellationReason
      ).includes(search.toUpperCase() as CancellationReason);

      const searchConditions = {
        OR: [
          // UUID fields - only search if the search term is a valid UUID
          ...(isSearchUUID ? [
            { id: { equals: search } },
            { customer_id: { equals: search } },
            { origin_ao_id: { equals: search } },
            { dest_ao_id: { equals: search } },
            { assigned_car_operator_id: { equals: search } },
            { updated_by: { equals: search } },
          ] : []),

          // Enum fields
          ...(isValidStatus ? [{ status: { equals: search.toUpperCase() } }] : []),
          ...(isValidCancellationReason ? [{ cancellation_reason: { equals: search.toUpperCase() } }] : []),

          // Numeric fields
          ...(!isNaN(searchFloat) ? [{ weight: { equals: searchFloat } }] : []),

          // Text fields - always searchable with contains
          { size: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
          { pickup_code: { contains: search, mode: "insensitive" } },
          { tracking_code: { contains: search, mode: "insensitive" } },
          { receiver_name: { contains: search, mode: "insensitive" } },
          { receiver_phone: { contains: search, mode: "insensitive" } },

          // Date fields - only search if the search term is a valid date
          ...(searchDate ? [
            { estimated_delivery: { equals: searchDate } },
            { picked_up_at: { equals: searchDate } },
            { cancelled_at: { equals: searchDate } },
            { expires_at: { equals: searchDate } },
            { created_at: { equals: searchDate } },
            { updated_at: { equals: searchDate } },
          ] : []),
        ].filter(Boolean),
      };

      where = {
        AND: [where.AND ? where : baseWhere, searchConditions],
      };
    }

    // Build orderBy clause
    let orderBy: any = { created_at: "desc" }; // default
    if (sort) {
      const [field, direction] = sort.split(":");
      const sortField = this.mapSortField(field);
      orderBy = { [sortField]: direction === "asc" ? "asc" : "desc" };
    }

    // Get total count for filtered results
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination and filtering
    const shipments = await this.prisma.shipment.findMany({
      where,
      orderBy,
      skip,
      take,
    });

    // Calculate stats for the access operator (all shipments, not just filtered)
    const stats = await this.calculateShipmentStats(baseWhere);

    return { shipments: shipments as Shipment[], total, stats };
  }

  async getPendingShipmentsByCustomerId(
    customerId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take } = options || {};

    const where = {
      customer_id: customerId,
      status: ShipmentStatus.PENDING,
      expires_at: { gt: new Date() },
    };

    // Get total count
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination
    const shipments = await this.prisma.shipment.findMany({
      where,
      orderBy: { created_at: "desc" },
      skip,
      take,
    });

    return { shipments: shipments as Shipment[], total };
  }

  async getPendingShipmentsByAccessOperatorId(
    aoId: string,
    options?: { skip?: number; take?: number; role?: string }
  ): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take, role } = options || {};

    // Build role-based filter
    let roleFilter: any;
    if (role === 'origin') {
      // Only shipments where AO is the origin (pickup point)
      roleFilter = { origin_ao_id: aoId };
    } else if (role === 'destination') {
      // Only shipments where AO is the destination (delivery point)
      roleFilter = { dest_ao_id: aoId };
    } else {
      // Default: both origin and destination (role === 'both' or no role specified)
      roleFilter = { OR: [{ origin_ao_id: aoId }, { dest_ao_id: aoId }] };
    }

    const where = {
      AND: [
        roleFilter,
        {
          status: ShipmentStatus.PENDING,
        },
        {
          expires_at: { gt: new Date() },
        },
      ],
    };

    // Get total count
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination
    const shipments = await this.prisma.shipment.findMany({
      where,
      orderBy: { created_at: "desc" },
      skip,
      take,
    });

    return { shipments: shipments as Shipment[], total };
  }

  async getPendingShipmentsForCarOperator(
    carOperatorId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take } = options || {};

    // For car operators, get pending shipments that they are eligible to transport
    // This would typically involve checking their registered routes, but for now
    // we'll return all pending shipments as they might be eligible for any route
    const where = {
      status: ShipmentStatus.PENDING,
      expires_at: { gt: new Date() },
    };

    // Get total count
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination
    const shipments = await this.prisma.shipment.findMany({
      where,
      orderBy: { created_at: "desc" },
      skip,
      take,
    });

    return { shipments: shipments as Shipment[], total };
  }

  async getShipmentsByCarOperatorAccessPoints(
    carOperatorId: string,
    options?: { skip?: number; take?: number }
  ): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take } = options || {};

    // First, get the car operator's access points
    const carOperator = await this.prisma.carOperator.findUnique({
      where: { id: carOperatorId },
      select: {
        pickup_access_point_id: true,
        dropoff_access_point_id: true,
      },
    });

    if (!carOperator) {
      throw new Error("Car operator not found");
    }

    // Build the where clause to find shipments assigned to the CO's access points
    const accessPointIds = [
      carOperator.pickup_access_point_id,
      carOperator.dropoff_access_point_id,
    ].filter(Boolean); // Remove null values

    if (accessPointIds.length === 0) {
      return { shipments: [], total: 0 };
    }

    // Filter out null values from accessPointIds
    const validAccessPointIds = accessPointIds.filter(
      (id) => id !== null
    ) as string[];

    const where = {
      OR: [
        { origin_ao_id: { in: validAccessPointIds } },
        { dest_ao_id: { in: validAccessPointIds } },
      ],
    };

    // Get total count
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination and include related data
    const shipments = await this.prisma.shipment.findMany({
      where,
      include: {
        originAO: {
          select: {
            id: true,
            business_name: true,
            address: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
        destAO: {
          select: {
            id: true,
            business_name: true,
            address: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        assignedCarOperator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
      skip,
      take,
    });

    return { shipments: shipments as Shipment[], total };
  }

  async getShipmentsByCarOperator(
    carOperatorId: string,
    options?: {
      skip?: number;
      take?: number;
      search?: string;
      status?: string;
      sort?: string;
      originAoId?: string;
      destAoId?: string;
    }
  ): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take, search, status, sort, originAoId, destAoId } =
      options || {};

    // Get the car operator's access points
    // Note: carOperatorId is actually the user_id, and CarOperator.id = User.id (one-to-one relationship)
    const carOperator = await this.prisma.carOperator.findUnique({
      where: { id: carOperatorId },
      select: {
        pickup_access_point_id: true,
        dropoff_access_point_id: true,
        approved: true,
      },
    });

    if (!carOperator) {
      console.error(
        `Car operator profile not found for user ID: ${carOperatorId}`
      );
      // Return empty result for incomplete profiles instead of throwing error
      return {
        shipments: [],
        total: 0,
        // message: 'Please complete your driver profile to view available shipments.'
      };
    }

    // Only show shipments if car operator is approved
    if (!carOperator.approved) {
      return {
        shipments: [],
        total: 0,
        // message: 'Your driver profile is pending approval. You will be able to view shipments once approved.'
      };
    }

    // Build base access points filter
    const accessPointIds = [
      carOperator.pickup_access_point_id,
      carOperator.dropoff_access_point_id,
    ].filter(Boolean);

    let baseWhere: any;
    if (accessPointIds.length > 0) {
      baseWhere = {
        OR: [
          { origin_ao_id: { in: accessPointIds } },
          { dest_ao_id: { in: accessPointIds } },
          { assigned_car_operator_id: carOperatorId },
        ],
      };
    } else {
      baseWhere = { assigned_car_operator_id: carOperatorId };
    }

    // Apply additional AO filters if specified
    if (originAoId && originAoId !== "") {
      // Filter to only show shipments from specific origin AO
      baseWhere = {
        AND: [baseWhere, { origin_ao_id: originAoId }],
      };
    }

    if (destAoId && destAoId !== "") {
      // Filter to only show shipments to specific destination AO
      baseWhere = {
        AND: [baseWhere, { dest_ao_id: destAoId }],
      };
    }

    let where: any = baseWhere;

    // Add status filter
    if (status && status !== "") {
      // Ensure status is uppercase to match enum values
      const normalizedStatus = status.toUpperCase();
      where = {
        AND: [baseWhere, { status: normalizedStatus }],
      };
    }

    // Add search filter
    if (search && search.trim() !== "") {
      const searchFloat = parseFloat(search);
      const searchDate = isNaN(Date.parse(search))
        ? undefined
        : new Date(search);
      const isSearchUUID = isUUID(search);

      // Check if search term is a valid enum value
      const isValidStatus = Object.values(ShipmentStatus).includes(
        search.toUpperCase() as ShipmentStatus
      );
      const isValidCancellationReason = Object.values(
        CancellationReason
      ).includes(search.toUpperCase() as CancellationReason);

      const orConditions = [
        isSearchUUID ? { id: { equals: search } } : undefined,
        isSearchUUID ? { customer_id: { equals: search } } : undefined,
        isSearchUUID ? { origin_ao_id: { equals: search } } : undefined,
        isSearchUUID ? { dest_ao_id: { equals: search } } : undefined,
        isSearchUUID
          ? { assigned_car_operator_id: { equals: search } }
          : undefined,
        isValidStatus
          ? { status: { equals: search.toUpperCase() } }
          : undefined,
        !isNaN(searchFloat) ? { weight: { equals: searchFloat } } : undefined,
        { size: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { pickup_code: { contains: search, mode: "insensitive" } },
        { tracking_code: { contains: search, mode: "insensitive" } },
        { receiver_name: { contains: search, mode: "insensitive" } },
        { receiver_phone: { contains: search, mode: "insensitive" } },
        searchDate ? { estimated_delivery: { equals: searchDate } } : undefined,
        searchDate ? { picked_up_at: { equals: searchDate } } : undefined,
        isValidCancellationReason
          ? { cancellation_reason: { equals: search.toUpperCase() } }
          : undefined,
        searchDate ? { cancelled_at: { equals: searchDate } } : undefined,
        searchDate ? { expires_at: { equals: searchDate } } : undefined,
        searchDate ? { created_at: { equals: searchDate } } : undefined,
        searchDate ? { updated_at: { equals: searchDate } } : undefined,
        isSearchUUID ? { updated_by: { equals: search } } : undefined, // UUID field should use equals, not contains
      ].filter((cond) => cond && Object.values(cond)[0] !== undefined);
      where = {
        AND: [
          where.AND ? where : baseWhere,
          {
            OR: orConditions,
          },
        ],
      };
    }

    // Build orderBy clause
    let orderBy: any = { created_at: "desc" }; // default
    if (sort) {
      const [field, direction] = sort.split(":");
      const sortField = this.mapSortField(field);
      orderBy = { [sortField]: direction === "asc" ? "asc" : "desc" };
    }

    // Get total count
    const total = await this.prisma.shipment.count({ where });

    // Get shipments with pagination and include related data
    const shipments = await this.prisma.shipment.findMany({
      where,
      include: {
        originAO: {
          select: {
            id: true,
            business_name: true,
            address: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
        destAO: {
          select: {
            id: true,
            business_name: true,
            address: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        assignedCarOperator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy,
      skip,
      take,
    });

    return { shipments: shipments as Shipment[], total };
  }

  async getShipmentByQRCode(qrCode: string): Promise<Shipment | null> {
    const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
      where: { qr_value: qrCode },
      include: { shipment: true },
    });

    if (!qrLabel || !qrLabel.shipment) {
      return null;
    }

    return qrLabel.shipment as Shipment;
  }

  async getShipmentByPickupCode(pickupCode: string): Promise<Shipment | null> {
    const shipment = await this.prisma.shipment.findFirst({
      where: { pickup_code: pickupCode },
    });

    return shipment as Shipment | null;
  }

  async validatePickupCode(
    shipmentId: string,
    pickupCode: string
  ): Promise<boolean> {
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) {
      return false;
    }

    return shipment.pickup_code === pickupCode;
  }

  /**
   * Scan shipment QR code and update status based on action
   */
  async scanShipment(
    userId: string,
    userType: string,
    shipmentId: string,
    aoId: string,
    customerId: string,
    photoUrl: string,
    action: "DROPOFF" | "PICKUP" | "ARRIVAL",
    notes?: string,
    geoLatitude?: number,
    geoLongitude?: number
  ): Promise<{ message: string; shipment: any }> {
    // Get shipment
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) {
      throw new Error("Shipment not found");
    }

    // Validate that photo URL is provided
    if (!photoUrl) {
      throw new Error("Photo is required for all shipment scan operations");
    }

    let newStatus: ShipmentStatus;
    let message: string;

    switch (action) {
      case "DROPOFF":
        if (userType !== "ACCESS_OPERATOR") {
          throw new Error("Only Access Operators can perform dropoff");
        }
        if (shipment.status !== ShipmentStatus.PENDING) {
          throw new Error("Shipment must be in PENDING status for dropoff");
        }
        newStatus = ShipmentStatus.AWAITING_PICKUP;
        message = "Package received successfully";
        break;

      case "PICKUP":
        if (userType !== "CAR_OPERATOR") {
          throw new Error("Only Car Operators can perform pickup");
        }
        if (shipment.status !== ShipmentStatus.AWAITING_PICKUP) {
          throw new Error(
            "Shipment must be in AWAITING_PICKUP status for pickup"
          );
        }
        newStatus = ShipmentStatus.IN_TRANSIT;
        message = "Package picked up successfully";
        break;

      case "ARRIVAL":
        if (userType !== "ACCESS_OPERATOR") {
          throw new Error("Only Access Operators can confirm arrival");
        }
        if (shipment.status !== ShipmentStatus.IN_TRANSIT) {
          throw new Error("Shipment must be in IN_TRANSIT status for arrival");
        }
        newStatus = ShipmentStatus.ARRIVED_AT_DESTINATION;
        message = "Package arrival confirmed";
        break;

      default:
        throw new Error("Invalid action");
    }

    // Update shipment status with updatedBy tracking
    const updatedShipment = await this.prisma.shipment.update({
      where: { id: shipmentId },
      data: this.addUpdatedBy(
        {
          status: newStatus,
          ...(newStatus === ShipmentStatus.AWAITING_PICKUP &&
          shipment.status === ShipmentStatus.PENDING
            ? { expires_at: null }
            : {}),
          updated_at: new Date(),
        },
        userId
      ),
    });

    // Create audit log
    await this.auditLogService.createAuditLog(
      userId,
      `SHIPMENT_${action}`,
      shipmentId,
      {
        action,
        photo_url: photoUrl,
        notes,
        geo_latitude: geoLatitude,
        geo_longitude: geoLongitude,
        previous_status: shipment.status,
        new_status: newStatus,
      }
    );

    return {
      message,
      shipment: {
        id: updatedShipment.id,
        status: updatedShipment.status,
        updated_at: updatedShipment.updated_at,
      },
    };
  }

  /**
   * Enhanced scan shipment with improved validation and security
   * Geo coordinates are automatically extracted based on action and AO location
   */
  async scanShipmentEnhanced(
    userId: string,
    userType: string,
    shipmentId: string,
    qrValue: string,
    photoUrl: string,
    action: "DROPOFF" | "PICKUP" | "ARRIVAL",
    notes?: string
  ): Promise<{ message: string; shipment: any }> {
    // Get shipment with AO details for geo coordinates
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        originAO: {
          select: {
            id: true,
            business_name: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
        destAO: {
          select: {
            id: true,
            business_name: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
      },
    });

    if (!shipment) {
      throw AppError.notFound("Shipment not found");
    }

    // Auto-extract geo coordinates based on action
    let geoLatitude: number | undefined;
    let geoLongitude: number | undefined;

    switch (action) {
      case "DROPOFF":
        // For DROPOFF: Use origin AO coordinates (where package is being dropped off)
        if (shipment.originAO) {
          geoLatitude = shipment.originAO.geo_latitude || undefined;
          geoLongitude = shipment.originAO.geo_longitude || undefined;
        }
        break;
      case "PICKUP":
        // For PICKUP: Use origin AO coordinates (where package is being picked up from)
        if (shipment.originAO) {
          geoLatitude = shipment.originAO.geo_latitude || undefined;
          geoLongitude = shipment.originAO.geo_longitude || undefined;
        }
        break;
      case "ARRIVAL":
        // For ARRIVAL: Use destination AO coordinates (where package is arriving)
        if (shipment.destAO) {
          geoLatitude = shipment.destAO.geo_latitude || undefined;
          geoLongitude = shipment.destAO.geo_longitude || undefined;
        }
        break;
    }

    console.info("Auto-extracted geo coordinates:", {
      action,
      geoLatitude,
      geoLongitude,
      originAO: shipment.originAO?.business_name,
      destAO: shipment.destAO?.business_name,
    });

    // Validate that photo URL is provided
    if (!photoUrl) {
      throw AppError.badRequest(
        "Photo is required for all shipment scan operations"
      );
    }

    // Validate QR code format and ownership
    // Handle both AO_ prefixed QR codes and legacy format QR codes
    if (qrValue.startsWith("AO_") || !qrValue.startsWith("PICKUP")) {
      // For AO QR codes (both new AO_ format and legacy format), validate ownership
      const qrLabel = await this.prisma.shipmentQRLabel.findUnique({
        where: { qr_value: qrValue },
      });

      console.info("=== QR VALIDATION DEBUG ===");
      console.info("QR Value:", qrValue);
      console.info("Action:", action);
      console.info("User Type:", userType);
      console.info("Shipment ID:", shipmentId);
      console.info("QR starts with AO_:", qrValue.startsWith("AO_"));
      console.info("QR starts with PICKUP:", qrValue.startsWith("PICKUP"));
      console.info("Scanning User ID:", userId);
      console.info("QR Label found:", !!qrLabel);
      if (qrLabel) {
        console.info("QR Label ao_id:", qrLabel.ao_id);
        console.info("QR Label status:", qrLabel.status);
        console.info("QR Label shipment_id:", qrLabel.shipment_id);
        console.info(
          "QR shipment matches scan shipment:",
          qrLabel.shipment_id === shipmentId
        );
        console.info("ao_id === userId:", qrLabel.ao_id === userId);
        console.info("ao_id type:", typeof qrLabel.ao_id);
        console.info("userId type:", typeof userId);
      }
      console.info("=== END QR VALIDATION DEBUG ===");

      if (!qrLabel) {
        throw AppError.notFound("QR code not found in system");
      }

      // Validate QR ownership based on user type and action
      if (userType === "ACCESS_OPERATOR") {
        if (action === "DROPOFF") {
          // For DROPOFF: Any AO (origin or destination) for the shipment can scan, as long as the QR is assigned to the shipment
          if (
            userId !== shipment.origin_ao_id &&
            userId !== shipment.dest_ao_id
          ) {
            throw AppError.forbidden(
              "Only the origin or destination Access Operator for this shipment can perform DROPOFF"
            );
          }
        } else if (action === "ARRIVAL") {
          // For ARRIVAL: Destination AO can scan any QR code (they don't own it)
          // No ownership validation needed - any AO can confirm arrival
        } else {
          throw AppError.forbidden(
            "Access Operators can only perform DROPOFF or ARRIVAL actions"
          );
        }
      } else if (userType === "CAR_OPERATOR") {
        if (action === "PICKUP") {
          // For PICKUP: Car Operators can scan any AO's QR codes
          // No ownership validation needed - they pick up from different AOs
        } else {
          throw AppError.forbidden(
            "Car Operators can only perform PICKUP action"
          );
        }
      } else {
        throw AppError.forbidden("Invalid user type for QR code scanning");
      }

      // Validate QR status based on action and shipment status
      // The same QR code is used throughout the package journey, so we need to check
      // if the QR is being used for the correct stage of the shipment
      if (action === "DROPOFF") {
        // For DROPOFF: QR should be ASSIGNED to this shipment (not UNUSED or USED for other shipments)
        if (qrLabel.status === QRLabelStatus.UNUSED) {
          throw AppError.badRequest(
            "QR code must be assigned to a shipment before DROPOFF"
          );
        }
        if (qrLabel.shipment_id !== shipmentId) {
          throw AppError.badRequest(
            "QR code is assigned to a different shipment"
          );
        }
      } else if (action === "PICKUP" || action === "ARRIVAL") {
        // For PICKUP and ARRIVAL: QR should be USED (from previous DROPOFF) and assigned to this shipment
        if (qrLabel.status !== QRLabelStatus.USED) {
          throw AppError.badRequest(
            `QR code must be used (from DROPOFF) before ${action}`
          );
        }
        if (qrLabel.shipment_id !== shipmentId) {
          throw AppError.badRequest(
            "QR code is assigned to a different shipment"
          );
        }
      }
    }

    let newStatus: ShipmentStatus;
    let message: string;

    switch (action) {
      case "DROPOFF":
        if (userType !== "ACCESS_OPERATOR") {
          throw AppError.forbidden("Only Access Operators can perform dropoff");
        }
        if (userId === shipment.origin_ao_id) {
          if (shipment.status !== ShipmentStatus.PENDING) {
            throw AppError.badRequest(
              "Shipment must be in PENDING status for dropoff at origin"
            );
          }
          newStatus = ShipmentStatus.AWAITING_PICKUP;
          message = "Package received successfully at origin";
        } else if (userId === shipment.dest_ao_id) {
          if (shipment.status !== ShipmentStatus.IN_TRANSIT) {
            throw AppError.badRequest(
              "Shipment must be in IN_TRANSIT status for dropoff at destination"
            );
          }
          newStatus = ShipmentStatus.ARRIVED_AT_DESTINATION;
          message = "Package received successfully at destination";
        } else {
          throw AppError.forbidden(
            "Only the origin or destination Access Operator for this shipment can perform DROPOFF"
          );
        }
        break;

      case "PICKUP":
        if (userType !== "CAR_OPERATOR") {
          throw AppError.forbidden("Only Car Operators can perform pickup");
        }
        if (shipment.status !== ShipmentStatus.AWAITING_PICKUP) {
          throw AppError.badRequest(
            "Shipment must be in AWAITING_PICKUP status for pickup"
          );
        }
        newStatus = ShipmentStatus.IN_TRANSIT;
        message = "Package picked up successfully by Car Operator";
        break;

      case "ARRIVAL":
        if (userType !== "ACCESS_OPERATOR") {
          throw AppError.forbidden("Only Access Operators can confirm arrival");
        }
        if (shipment.status !== ShipmentStatus.IN_TRANSIT) {
          throw AppError.badRequest(
            "Shipment must be in IN_TRANSIT status for arrival"
          );
        }
        newStatus = ShipmentStatus.ARRIVED_AT_DESTINATION;
        message = "Package arrival confirmed at destination";
        break;

      default:
        throw AppError.badRequest("Invalid action");
    }

    // Update shipment status
    const updateData: any = {
      status: newStatus,
      ...(newStatus === ShipmentStatus.AWAITING_PICKUP &&
      shipment.status === ShipmentStatus.PENDING
        ? { expires_at: null }
        : {}),
      updated_at: new Date(),
    };
    const updatedShipment = await this.prisma.shipment.update({
      where: { id: shipmentId },
      data: updateData,
    });

    // Store photo for this operation
    await this.storeShipmentPhoto(
      shipmentId,
      userId,
      photoUrl,
      action,
      notes,
      geoLatitude,
      geoLongitude
    );

    // Update QR code status based on action
    if (qrValue.startsWith("AO_")) {
      if (action === "DROPOFF") {
        // Mark QR code as USED only after DROPOFF (first use)
        await this.prisma.shipmentQRLabel.updateMany({
          where: { qr_value: qrValue },
          data: { status: QRLabelStatus.USED },
        });
      }
      // For PICKUP and ARRIVAL, QR code remains USED (no status change needed)
    }

    // Create audit log
    await this.auditLogService.createAuditLog(
      userId,
      `SHIPMENT_${action}`,
      shipmentId,
      {
        action,
        qr_value: qrValue,
        photo_url: photoUrl,
        notes,
        geo_latitude: geoLatitude,
        geo_longitude: geoLongitude,
        previous_status: shipment.status,
        new_status: newStatus,
      }
    );

    // Send comprehensive scan notifications
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(shipmentId);
        if (context) {
          // Add action user information to context
          context.actionUserId = userId;
          context.actionUserType = userType as any;

          // Send appropriate notifications based on scan action
          switch (action) {
            case "DROPOFF":
              await this.shipmentNotificationService.notifyDropoffScan(context);
              break;
            case "PICKUP":
              await this.shipmentNotificationService.notifyPickupScan(context);
              break;
            case "ARRIVAL":
              await this.shipmentNotificationService.notifyArrivalScan(context);
              // Also send legacy receiver notification for backward compatibility
              await this.sendReceiverNotification(shipment);
              break;
          }
        }
      } else {
        // Fallback to basic notification for ARRIVAL
        if (newStatus === ShipmentStatus.ARRIVED_AT_DESTINATION) {
          await this.sendReceiverNotification(shipment);
        }
      }
    } catch (error) {
      console.error("❌ Failed to send scan notifications:", error);
      // Don't throw error to avoid breaking scan operation
    }

    return {
      message,
      shipment: {
        id: updatedShipment.id,
        status: updatedShipment.status,
        updated_at: updatedShipment.updated_at,
      },
    };
  }

  /**
   * Final delivery with both QR codes validation (enhanced security)
   * Geo coordinates are automatically extracted from destination AO
   */
  async deliverShipment(
    userId: string,
    shipmentId: string,
    pickupCode: string,
    photoUrl: string,
    notes?: string,
    receiverPhone?: string
  ): Promise<{ message: string; shipment: any }> {
    // Get shipment with destination AO details for geo coordinates
    const shipment = await this.prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        destAO: {
          select: {
            id: true,
            business_name: true,
            geo_latitude: true,
            geo_longitude: true,
          },
        },
      },
    });

    if (!shipment) {
      console.error("[DELIVER ERROR] Shipment not found", { shipmentId });
      throw new Error("Shipment not found");
    }

    // Auto-extract geo coordinates from destination AO (where delivery is happening)
    const geoLatitude = shipment.destAO?.geo_latitude || undefined;
    const geoLongitude = shipment.destAO?.geo_longitude || undefined;

    console.info("Auto-extracted delivery geo coordinates:", {
      geoLatitude,
      geoLongitude,
      destAO: shipment.destAO?.business_name,
    });

    // Validate that photo URL is provided
    if (!photoUrl) {
      console.error("[DELIVER ERROR] Photo is required", {
        shipmentId,
        userId,
      });
      throw new Error("Photo is required for shipment delivery");
    }

    // Validate status
    if (shipment.status !== ShipmentStatus.ARRIVED_AT_DESTINATION) {
      console.error("[DELIVER ERROR] Invalid status for delivery", {
        shipmentId,
        status: shipment.status,
      });
      throw new Error(
        "Shipment must be in ARRIVED_AT_DESTINATION status for delivery"
      );
    }

    // Validate pickup code
    if (shipment.pickup_code !== pickupCode) {
      console.error("[DELIVER ERROR] Invalid pickup code", {
        shipmentId,
        provided: pickupCode,
        expected: shipment.pickup_code,
      });
      throw new Error("Invalid pickup code");
    }

    // Validate receiver phone if provided
    if (receiverPhone && receiverPhone !== shipment.receiver_phone) {
      console.error("[DELIVER ERROR] Receiver phone does not match", {
        shipmentId,
        provided: receiverPhone,
        expected: shipment.receiver_phone,
      });
      throw new Error("Invalid receiver phone number");
    }

    // Additional security: Validate that the user is authorized to deliver this shipment
    if (shipment.dest_ao_id !== userId) {
      // Log security event
      console.error("[DELIVER FORBIDDEN] User is not destination AO", {
        userId,
        shipmentId,
        expected_ao_id: shipment.dest_ao_id,
      });
      await this.auditLogService.createAuditLog(
        userId,
        "UNAUTHORIZED_DELIVERY_ATTEMPT",
        shipmentId,
        {
          reason: "User not authorized for this shipment destination",
          expected_ao_id: shipment.dest_ao_id,
          actual_user_id: userId,
          pickup_code: pickupCode,
        }
      );
      throw new Error("You are not authorized to deliver this shipment");
    }

    // Update shipment status and receiver phone if provided
    const updateData: any = {
      status: ShipmentStatus.DELIVERED,
      expires_at: null, // Clear expiry once delivered
      updated_at: new Date(),
    };
    if (receiverPhone) {
      updateData.receiver_phone = receiverPhone;
    }
    const updatedShipment = await this.prisma.shipment.update({
      where: { id: shipmentId },
      data: updateData,
    });

    // Store photo for delivery operation
    await this.storeShipmentPhoto(
      shipmentId,
      userId,
      photoUrl,
      "DELIVERY",
      notes,
      geoLatitude,
      geoLongitude
    );

    // Create audit log
    await this.auditLogService.createAuditLog(
      userId,
      "SHIPMENT_DELIVERED",
      shipmentId,
      {
        pickup_code: pickupCode,
        photo_url: photoUrl,
        notes,
        geo_latitude: geoLatitude,
        geo_longitude: geoLongitude,
        delivered_at: new Date(),
        receiver_name: shipment.receiver_name,
        receiver_phone: receiverPhone || shipment.receiver_phone,
      }
    );

    // Send comprehensive delivery notifications
    try {
      if (this.shipmentNotificationService) {
        const context =
          await this.shipmentNotificationService.getShipmentContext(shipmentId);
        if (context) {
          // Add action user information to context
          context.actionUserId = userId;
          await this.shipmentNotificationService.notifyDelivery(context);
        }
      } else {
        // Fallback to basic notification
        await this.notificationService.createNotification(
          shipment.customer_id,
          shipment.id,
          "SHIPMENT_DELIVERED",
          `Your shipment has been successfully delivered to ${shipment.receiver_name}.`
        );
      }
    } catch (error) {
      console.error("❌ Failed to send delivery notifications:", error);
      // Don't throw error to avoid breaking delivery operation
    }

    return {
      message: "Package delivered successfully to receiver",
      shipment: {
        id: updatedShipment.id,
        status: updatedShipment.status,
        updated_at: updatedShipment.updated_at,
        receiver_name: shipment.receiver_name,
        receiver_phone: receiverPhone || shipment.receiver_phone,
      },
    };
  }

  /**
   * Send notification to receiver when package arrives at destination
   */
  private async sendReceiverNotification(shipment: any): Promise<void> {
    try {
      // Generate pickup QR link (this would be a frontend URL in production)
      const pickupQRLink = `${
        process.env.FRONTEND_URL || "http://localhost:3000"
      }/pickup-qr/${shipment.id}`;

      // Create notification for the customer about receiver notification
      await this.notificationService.createNotification(
        shipment.customer_id,
        shipment.id,
        "PACKAGE_ARRIVED_FOR_RECEIVER",
        `Your package has arrived at the destination. Receiver ${shipment.receiver_name} (${shipment.receiver_phone}) can now collect it using pickup code: ${shipment.pickup_code}`
      );

      // Log the notification
      await this.auditLogService.createAuditLog(
        shipment.customer_id,
        "RECEIVER_NOTIFICATION_SENT",
        shipment.id,
        {
          receiver_name: shipment.receiver_name,
          receiver_phone: shipment.receiver_phone,
          pickup_code: shipment.pickup_code,
          pickup_qr_link: pickupQRLink,
        }
      );
    } catch (error) {
      console.error("Failed to send receiver notification:", error);
      // Don't throw error as this shouldn't block the main workflow
    }
  }

  /**
   * Validate that there's a Car Operator available for the origin-destination route
   */
  private async validateRouteAvailability(
    originAoId: string,
    destAoId: string
  ): Promise<void> {
    // Find Car Operators that can handle this route
    // A CO can handle the route if they have both AOs in their access points
    // (either as pickup/dropoff or dropoff/pickup)
    const availableCarOperators = await this.prisma.carOperator.findMany({
      where: {
        AND: [
          { approved: true },
          {
            OR: [
              // Exact match: pickup from origin, dropoff at destination
              {
                AND: [
                  { pickup_access_point_id: originAoId },
                  { dropoff_access_point_id: destAoId },
                ],
              },
              // Reverse match: pickup from destination, dropoff at origin
              // This allows COs to work in both directions between their 2 AOs
              {
                AND: [
                  { pickup_access_point_id: destAoId },
                  { dropoff_access_point_id: originAoId },
                ],
              },
            ],
          },
        ],
      },
      include: {
        user: {
          select: {
            status: true,
          },
        },
      },
    });

    // Filter for active users
    const activeCarOperators = availableCarOperators.filter(
      (co) => co.user.status === "ACTIVE"
    );

    if (activeCarOperators.length === 0) {
      throw AppError.badRequest(
        `No Car Operator available for route. ` +
          "Please select different Access Operators or contact support."
      );
    }
  }

  /**
   * Store photo for shipment operation
   */
  private async storeShipmentPhoto(
    shipmentId: string,
    userId: string,
    photoUrl: string,
    action: string,
    notes?: string,
    latitude?: number,
    longitude?: number
  ): Promise<void> {
    // Map action to ShipmentPhotoAction enum
    let photoAction: string;
    switch (action) {
      case "DROPOFF":
        photoAction = "DROPOFF";
        break;
      case "PICKUP":
        photoAction = "PICKUP";
        break;
      case "ARRIVAL":
        photoAction = "ARRIVAL";
        break;
      case "DELIVERY":
        photoAction = "DELIVERY";
        break;
      default:
        photoAction = "QR_ASSIGNMENT";
    }

    await this.prisma.shipmentPhoto.create({
      data: {
        shipment_id: shipmentId,
        user_id: userId,
        photo_url: photoUrl,
        action: photoAction as any, // Type assertion for enum
        notes,
        latitude,
        longitude,
      },
    });
  }

  /**
   * Map frontend sort field names to database field names
   */
  private mapSortField(field: string): string {
    const fieldMapping: Record<string, string> = {
      id: "id",
      status: "status",
      receiverName: "receiver_name",
      createdAt: "created_at",
      created_at: "created_at", // Support both formats
      updatedAt: "updated_at",
      updated_at: "updated_at", // Support both formats
      weight: "weight",
      size: "size",
      pickupCode: "pickup_code",
      pickup_code: "pickup_code", // Support both formats
    };

    return fieldMapping[field] || "created_at";
  }

  /**
   * Calculate shipment statistics
   */
  private async calculateShipmentStats(where: any): Promise<any> {
    const [
      total,
      pending,
      awaitingPickup,
      inTransit,
      arrivedAtDestination,
      delivered,
      cancelled,
    ] = await Promise.all([
      this.prisma.shipment.count({ where }),
      this.prisma.shipment.count({ where: { ...where, status: "PENDING" } }),
      this.prisma.shipment.count({
        where: { ...where, status: "AWAITING_PICKUP" },
      }),
      this.prisma.shipment.count({ where: { ...where, status: "IN_TRANSIT" } }),
      this.prisma.shipment.count({
        where: { ...where, status: "ARRIVED_AT_DESTINATION" },
      }),
      this.prisma.shipment.count({ where: { ...where, status: "DELIVERED" } }),
      this.prisma.shipment.count({ where: { ...where, status: "CANCELLED" } }),
    ]);

    return {
      total,
      pending,
      inTransit: awaitingPickup + inTransit + arrivedAtDestination,
      delivered,
      cancelled,
    };
  }
}
