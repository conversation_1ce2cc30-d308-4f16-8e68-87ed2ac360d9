# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
- `yarn dev` - Start development server with auto-reload
- `yarn build` - Compile TypeScript to JavaScript
- `yarn start` - Run production build
- `yarn validate` - Run linting and type checking together

### Testing & Quality
- `yarn test` - Run Jest test suite
- `yarn test:watch` - Run tests in watch mode
- `yarn test:coverage` - Generate test coverage reports
- `yarn lint` - Check code style with ESLint
- `yarn lint:fix` - Auto-fix linting issues
- `yarn type-check` - Run TypeScript compiler without emitting files

### Database Management
- `npx prisma migrate dev` - Apply database migrations in development
- `npx prisma generate` - Generate Prisma client
- `yarn reset-db` - Reset database (custom script)
- `yarn test-db` - Test database connection

### Utility Scripts
- `yarn list-users` - List all users in the system
- `yarn user-stats` - Show user statistics
- `yarn list-admins` - List all admin users
- `yarn add-admin` - Add a new admin user
- `yarn unapprove-user` - Unapprove a user

## Architecture Overview

This is a **decentralized shipment coordination platform** connecting customers, access operators (physical pickup/drop-off points), and car operators (drivers) for package delivery.

### Core Architecture Patterns

**Dependency Injection**: The application uses a ServiceFactory pattern for dependency injection, creating singleton service instances with proper dependency management.

**Service Layer Architecture**: 
- Controllers handle HTTP requests and validation
- Services contain business logic and database operations
- Middleware handles authentication, authorization, and request processing

**Key Services**:
- `UserService` - User management and authentication
- `ShipmentService` - Core shipment operations
- `NotificationService` - Multi-channel notifications
- `EmailService` - Email templating and delivery (supports Arabic/bilingual)
- `AdminService` - Administrative operations
- `QRLabelService` - QR code generation and management
- `AuditLogService` - Comprehensive audit logging

### Database Design

Uses **Prisma ORM** with PostgreSQL. Key models:
- `User` - All user types with role-based differentiation
- `Shipment` - Core shipment entity with status tracking
- `AccessOperator` - Physical pickup/drop-off locations
- `CarOperator` - Drivers with vehicle information
- `Admin` - Administrative users
- `Notification` - Multi-channel notification system
- `AuditLog` - Complete action tracking

### Multi-language Support

The platform supports Arabic and English:
- Email templates in `src/utils/arabicEmailTemplates.ts` and `src/utils/bilingualEmailTemplates.ts`
- User language preferences stored in database
- Modern email template system with internationalization

### Authentication & Authorization

- JWT-based authentication
- Role-based access control (Customer, Access Operator, Car Operator, Admin)
- Separate admin authentication system
- User status management (PENDING, APPROVED, SUSPENDED, etc.)

## Development Environment Setup

### Database
Start PostgreSQL using Docker:
```bash
docker-compose up -d
npx prisma migrate dev
```

Database credentials (development):
- Host: localhost:5432
- Database: naqalat_db  
- User: postgres
- Password: postgres

### Path Aliases
The project uses TypeScript path mapping:
- `@/*` maps to `src/*`

## Testing Strategy

Uses Jest for testing. The codebase had notification tests that were removed (as shown in git status), suggesting active test maintenance.

## Key Features to Understand

1. **QR Code System**: Shipments use QR codes for tracking and verification
2. **Photo Documentation**: Each handover step requires photo documentation
3. **Audit Trail**: Complete history of all actions for dispute resolution
4. **Multi-role System**: Different interfaces and permissions for each user type
5. **Notification System**: Real-time updates via multiple channels
6. **Status Management**: Complex state machine for shipment and user statuses

## API Documentation

Swagger documentation available at `/api-docs` when server is running.

## Important Notes

- Always run `yarn validate` before committing to ensure code quality
- The application follows SOLID principles with clean separation of concerns
- Database migrations are version-controlled through Prisma
- Photo uploads are handled through multer middleware
- Email system supports both Arabic and English templates