/* eslint-disable no-console */
import { PrismaClient } from "@prisma/client";

import { INotificationService } from "../interfaces/services";
import {
  Notification,
  NotificationPreference,
  NotificationType,
  NotificationPriority,
} from "../types/models";

export interface CreateNotificationData {
  userId: string;
  shipmentId?: string;
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

export interface NotificationFilters {
  read?: boolean;
  type?: NotificationType;
  priority?: NotificationPriority;
  shipmentId?: string;
  fromDate?: Date;
  toDate?: Date;
  search?: string;
}

export interface PaginationOptions {
  skip?: number;
  take?: number;
}

export class NotificationService implements INotificationService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Create a single notification
   */
  async createNotification(
    userId: string,
    shipmentId: string,
    type: string,
    message: string
  ): Promise<Notification> {
    // Legacy method - convert to new format
    return this.createNotificationEnhanced({
      userId,
      shipmentId,
      type: type as NotificationType,
      title: this.getNotificationTitle(type as NotificationType),
      message,
      priority: NotificationPriority.NORMAL,
    });
  }

  /**
   * Create a notification with enhanced options
   */
  async createNotificationEnhanced(
    data: CreateNotificationData
  ): Promise<Notification> {
    try {
      console.log(`📧 Creating notification for user ${data.userId}:`, {
        type: data.type,
        title: data.title,
        shipmentId: data.shipmentId,
      });

      const notification = await this.prisma.notification.create({
        data: {
          user_id: data.userId,
          shipment_id: data.shipmentId || null,
          notification_type: data.type,
          title: data.title,
          message: data.message,
          priority: data.priority || NotificationPriority.NORMAL,
          metadata: data.metadata || undefined,
          expires_at: data.expiresAt || null,
        },
      });

      console.log(`✅ Notification created successfully: ${notification.id}`);
      return notification as Notification;
    } catch (error) {
      console.error("❌ Failed to create notification:", error);
      throw new Error(
        `Failed to create notification: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Create multiple notifications for different users
   */
  async createBulkNotifications(
    notifications: CreateNotificationData[]
  ): Promise<Notification[]> {
    try {
      console.log(`📧 Creating ${notifications.length} bulk notifications`);

      const createdNotifications = await this.prisma.$transaction(
        notifications.map((data) =>
          this.prisma.notification.create({
            data: {
              user_id: data.userId,
              shipment_id: data.shipmentId || null,
              notification_type: data.type,
              title: data.title,
              message: data.message,
              priority: data.priority || NotificationPriority.NORMAL,
              metadata: data.metadata || undefined,
              expires_at: data.expiresAt || undefined,
            },
          })
        )
      );

      console.log(
        `✅ Created ${createdNotifications.length} bulk notifications`
      );
      return createdNotifications as Notification[];
    } catch (error) {
      console.error("❌ Failed to create bulk notifications:", error);
      throw new Error(
        `Failed to create bulk notifications: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get notifications for a user with filtering and pagination
   */
  async getNotificationsByUserId(
    userId: string,
    filters?: NotificationFilters,
    pagination?: PaginationOptions
  ): Promise<Notification[]> {
    try {
      const where: any = { user_id: userId };

      // Apply filters
      if (filters?.read !== undefined) {
        where.read = filters.read;
      }
      if (filters?.type) {
        where.notification_type = filters.type;
      }
      if (filters?.priority) {
        where.priority = filters.priority;
      }
      if (filters?.shipmentId) {
        where.shipment_id = filters.shipmentId;
      }
      if (filters?.fromDate || filters?.toDate) {
        where.created_at = {};
        if (filters.fromDate) {
          where.created_at.gte = filters.fromDate;
        }
        if (filters.toDate) {
          where.created_at.lte = filters.toDate;
        }
      }

      // Apply search filter
      if (filters?.search && filters.search.trim()) {
        const searchTerm = filters.search.trim();
        where.OR = [
          {
            title: {
              contains: searchTerm,
              mode: "insensitive" as const,
            },
          },
          {
            message: {
              contains: searchTerm,
              mode: "insensitive" as const,
            },
          },
        ];
      }

      const notifications = await this.prisma.notification.findMany({
        where,
        orderBy: { created_at: "desc" },
        skip: pagination?.skip || 0,
        take: pagination?.take || 50,
        include: {
          shipment: {
            select: {
              id: true,
              tracking_code: true,
              status: true,
            },
          },
        },
      });

      return notifications as Notification[];
    } catch (error) {
      console.error("❌ Failed to get notifications:", error);
      throw new Error(
        `Failed to get notifications: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get unread notifications for a user
   */
  async getUnreadNotificationsByUserId(
    userId: string
  ): Promise<Notification[]> {
    return this.getNotificationsByUserId(userId, { read: false });
  }

  /**
   * Get notification count for a user
   */
  async getNotificationCount(
    userId: string,
    filters?: NotificationFilters
  ): Promise<number> {
    try {
      const where: any = { user_id: userId };

      // Apply filters
      if (filters?.read !== undefined) {
        where.read = filters.read;
      }
      if (filters?.type) {
        where.notification_type = filters.type;
      }
      if (filters?.priority) {
        where.priority = filters.priority;
      }
      if (filters?.shipmentId) {
        where.shipment_id = filters.shipmentId;
      }
      if (filters?.fromDate || filters?.toDate) {
        where.created_at = {};
        if (filters.fromDate) {
          where.created_at.gte = filters.fromDate;
        }
        if (filters.toDate) {
          where.created_at.lte = filters.toDate;
        }
      }

      // Apply search filter
      if (filters?.search && filters.search.trim()) {
        const searchTerm = filters.search.trim();
        where.OR = [
          {
            title: {
              contains: searchTerm,
              mode: "insensitive" as const,
            },
          },
          {
            message: {
              contains: searchTerm,
              mode: "insensitive" as const,
            },
          },
        ];
      }

      return await this.prisma.notification.count({ where });
    } catch (error) {
      console.error("❌ Failed to get notification count:", error);
      throw new Error(
        `Failed to get notification count: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Mark a notification as read
   */
  async markNotificationAsRead(id: string): Promise<Notification> {
    try {
      const notification = await this.prisma.notification.update({
        where: { id },
        data: {
          read: true,
          read_at: new Date(),
        },
      });

      console.log(`✅ Marked notification ${id} as read`);
      return notification as Notification;
    } catch (error) {
      console.error("❌ Failed to mark notification as read:", error);
      throw new Error(
        `Failed to mark notification as read: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllNotificationsAsRead(userId: string): Promise<void> {
    try {
      const result = await this.prisma.notification.updateMany({
        where: {
          user_id: userId,
          read: false,
        },
        data: {
          read: true,
          read_at: new Date(),
        },
      });

      console.log(
        `✅ Marked ${result.count} notifications as read for user ${userId}`
      );
    } catch (error) {
      console.error("❌ Failed to mark all notifications as read:", error);
      throw new Error(
        `Failed to mark all notifications as read: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Delete old notifications (cleanup utility)
   */
  async deleteExpiredNotifications(): Promise<number> {
    try {
      const result = await this.prisma.notification.deleteMany({
        where: {
          expires_at: {
            lt: new Date(),
          },
        },
      });

      console.log(`🗑️ Deleted ${result.count} expired notifications`);
      return result.count;
    } catch (error) {
      console.error("❌ Failed to delete expired notifications:", error);
      throw new Error(
        `Failed to delete expired notifications: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get or create notification preferences for a user
   */
  async getNotificationPreferences(
    userId: string
  ): Promise<NotificationPreference> {
    try {
      let preferences = await this.prisma.notificationPreference.findUnique({
        where: { user_id: userId },
      });

      if (!preferences) {
        // Create default preferences
        preferences = await this.prisma.notificationPreference.create({
          data: {
            user_id: userId,
            email_notifications: true,
            sms_notifications: false,
            push_notifications: true,
            shipment_created: true,
            shipment_status_change: true,
            qr_assignment: true,
            package_ready: true,
            delivery_completed: true,
            preferred_language: 'ENGLISH',
            email_format: 'BILINGUAL',
          },
        });
      }

      return preferences as NotificationPreference;
    } catch (error) {
      console.error("❌ Failed to get notification preferences:", error);
      throw new Error(
        `Failed to get notification preferences: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Update notification preferences for a user
   */
  async updateNotificationPreferences(
    userId: string,
    preferences: Partial<
      Omit<
        NotificationPreference,
        "id" | "user_id" | "created_at" | "updated_at"
      >
    >
  ): Promise<NotificationPreference> {
    try {
      const updatedPreferences =
        await this.prisma.notificationPreference.upsert({
          where: { user_id: userId },
          update: preferences,
          create: {
            user_id: userId,
            email_notifications: preferences.email_notifications ?? true,
            sms_notifications: preferences.sms_notifications ?? false,
            push_notifications: preferences.push_notifications ?? true,
            shipment_created: preferences.shipment_created ?? true,
            shipment_status_change: preferences.shipment_status_change ?? true,
            qr_assignment: preferences.qr_assignment ?? true,
            package_ready: preferences.package_ready ?? true,
            delivery_completed: preferences.delivery_completed ?? true,
            preferred_language: preferences.preferred_language ?? 'ENGLISH',
            email_format: preferences.email_format ?? 'BILINGUAL',
          },
        });

      console.log(`✅ Updated notification preferences for user ${userId}`);
      return updatedPreferences as NotificationPreference;
    } catch (error) {
      console.error("❌ Failed to update notification preferences:", error);
      throw new Error(
        `Failed to update notification preferences: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get notification title based on type
   */
  private getNotificationTitle(type: NotificationType): string {
    const titles: Record<NotificationType, string> = {
      [NotificationType.SHIPMENT_CREATED]: "📦 Shipment Created",
      [NotificationType.SHIPMENT_ASSIGNED_QR]: "🏷️ QR Code Assigned",
      [NotificationType.SHIPMENT_DROPPED_OFF]: "📤 Package Dropped Off",
      [NotificationType.SHIPMENT_PICKED_UP]: "🚚 Package Picked Up",
      [NotificationType.SHIPMENT_IN_TRANSIT]: "🚛 Package In Transit",
      [NotificationType.SHIPMENT_ARRIVED]: "📍 Package Arrived",
      [NotificationType.SHIPMENT_READY_FOR_DELIVERY]: "✅ Ready for Pickup",
      [NotificationType.SHIPMENT_DELIVERED]: "🎉 Package Delivered",
      [NotificationType.SHIPMENT_CANCELLED]: "❌ Shipment Cancelled",
      [NotificationType.SHIPMENT_EXPIRED]: "⏰ Shipment Expired",
      [NotificationType.SHIPMENT_STATUS_CHANGED]: "🔄 Status Updated",
      [NotificationType.QR_CODE_ASSIGNED]: "🏷️ QR Code Ready",
      [NotificationType.PACKAGE_READY_FOR_PICKUP]: "📦 Package Ready",
      [NotificationType.DELIVERY_REMINDER]: "⏰ Delivery Reminder",
      [NotificationType.SYSTEM_ALERT]: "🚨 System Alert",
      [NotificationType.USER_REGISTERED]: "👤 User Registered",
      [NotificationType.USER_EMAIL_VERIFIED]: "📧 User Email Verified",
      [NotificationType.OPERATOR_NEEDS_APPROVAL]: "📋 Operator Needs Approval",
      [NotificationType.USER_STATUS_CHANGED]: "🔄 User Status Changed",
      [NotificationType.SECURITY_ALERT]: "🔒 Security Alert",
      [NotificationType.SYSTEM_ERROR]: "💥 System Error",
      [NotificationType.ADMIN_CREATED]: "👑 Admin Created",
      [NotificationType.BULK_OPERATION_COMPLETED]: "📊 Bulk Operation Completed",
      [NotificationType.SYSTEM_MAINTENANCE]: "🛠️ System Maintenance",
    };

    return titles[type] || "📢 Notification";
  }

  /**
   * Check if user should receive notification based on preferences
   */
  async shouldNotifyUser(
    userId: string,
    type: NotificationType
  ): Promise<boolean> {
    try {
      const preferences = await this.getNotificationPreferences(userId);

      // Check specific notification type preferences
      switch (type) {
        case NotificationType.SHIPMENT_CREATED:
          return preferences.shipment_created;
        case NotificationType.SHIPMENT_STATUS_CHANGED:
        case NotificationType.SHIPMENT_DROPPED_OFF:
        case NotificationType.SHIPMENT_PICKED_UP:
        case NotificationType.SHIPMENT_IN_TRANSIT:
        case NotificationType.SHIPMENT_ARRIVED:
          return preferences.shipment_status_change;
        case NotificationType.QR_CODE_ASSIGNED:
        case NotificationType.SHIPMENT_ASSIGNED_QR:
          return preferences.qr_assignment;
        case NotificationType.PACKAGE_READY_FOR_PICKUP:
        case NotificationType.SHIPMENT_READY_FOR_DELIVERY:
          return preferences.package_ready;
        case NotificationType.SHIPMENT_DELIVERED:
          return preferences.delivery_completed;
        default:
          return true; // Default to sending notification
      }
    } catch (error) {
      console.error("❌ Failed to check notification preferences:", error);
      return true; // Default to sending notification if preferences check fails
    }
  }
}
