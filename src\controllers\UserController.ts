import { Request, Response, NextFunction } from "express";
import jwt, { SignOptions, Secret } from "jsonwebtoken";
import { z } from "zod";

import {
  UserLoginSchema,
  UserUpdateSchema,
  CustomerRegisterSchema,
  AccessOperatorRegisterSchema,
  CarOperatorRegisterSchema,
  PasswordSchema,
  SendOTPSchema,
  VerifyOTPSchema,
  ResendOTPSchema,
} from "../schemas";
import { ServiceFactory } from "../services/ServiceFactory";
import { UserType, UserStatus } from "../types/models";
import { validateRequest, AppError } from "../utils/errors";
import { parsePaginationParams } from "../utils/pagination";

export class UserController {
  // Password reset schema
  static PasswordResetSchema = UserLoginSchema.extend({
    token: z.string(),
  });

  // Email verification schema
  static EmailVerificationSchema = z.object({
    email: z.string().email(),
    token: z.string(),
  });

  // Change password schema
  static ChangePasswordSchema = z.object({
    currentPassword: PasswordSchema,
    newPassword: PasswordSchema,
  });
  /**
   * Validate register request based on user_type
   * This middleware first checks the user_type and then applies the appropriate schema
   */
  static validateRegister = (
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      // First, check if user_type is provided
      if (!req.body.user_type) {
        throw AppError.validation("Validation error", {
          user_type: ["User type is required"],
        });
      }

      // Select the appropriate schema based on user_type
      let schema;
      switch (req.body.user_type) {
        case UserType.CUSTOMER:
          console.info("Using CustomerRegisterSchema for validation");
          schema = CustomerRegisterSchema;
          break;
        case UserType.ACCESS_OPERATOR:
          console.info("Using AccessOperatorRegisterSchema for validation");
          schema = AccessOperatorRegisterSchema;
          break;
        case UserType.CAR_OPERATOR:
          console.info("Using CarOperatorRegisterSchema for validation");
          schema = CarOperatorRegisterSchema;
          break;
        default:
          throw AppError.validation("Validation error", {
            user_type: ["Invalid user type"],
          });
      }

      // Validate the request body against the selected schema
      try {
        req.body = schema.parse(req.body);
        console.info("Registration validation successful");
        next();
      } catch (error) {
        if (error instanceof z.ZodError) {
          console.error("Registration validation failed:", {
            user_type: req.body.user_type,
            errors: error.errors,
            requestBody: req.body,
          });

          // Format the Zod error for better readability
          const formattedError = error.errors.reduce((acc, curr) => {
            const path = curr.path.join(".");
            if (!acc[path]) {
              acc[path] = [];
            }
            acc[path].push(curr.message);
            return acc;
          }, {} as Record<string, string[]>);

          throw AppError.validation("Validation error", formattedError);
        }
        throw error;
      }
    } catch (error) {
      next(error);
    }
  };

  /**
   * Register a new user
   */
  static async register(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { name, email, password, phone, user_type } = req.body;

      const userService = ServiceFactory.getUserService();

      // Prepare base user data
      const userData = {
        name,
        email,
        password_hash: password,
        phone,
        user_type: user_type as UserType,
        status: UserStatus.ACTIVE, // Users are ACTIVE by default
        email_verified: false,
      };

      // Create user with basic information only
      await userService.createUser(userData);

      // Return success message with appropriate information based on user type
      let message = "User registered successfully. Please check your email to verify your account.";

      // Add approval information for operators
      if (user_type === UserType.ACCESS_OPERATOR || user_type === UserType.CAR_OPERATOR) {
        message += " After verification, your account will require admin approval before you can use the platform.";
      } else {
        message += " After verification, you can immediately start using the platform.";
      }

      res.status(201).json({
        success: true,
        message,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate login request
   */
  static validateLogin = validateRequest(UserLoginSchema);

  /**
   * Login a user
   */
  static async login(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { email, password } = req.body;

      // Validate credentials
      const userService = ServiceFactory.getUserService();
      const user = await userService.validateCredentials(email, password);

      if (!user) {
        res.status(401).json({
          success: false,
          message: "Invalid credentials",
          error: {
            type: "AUTHENTICATION_ERROR",
            details: "Email or password is incorrect",
          },
        });
        return;
      }

      // Check if email is verified
      if (!user.email_verified) {
        // Send a new verification OTP if needed
        const userService = ServiceFactory.getUserService();
        await userService.sendVerificationOTP(user.email);

        res.status(403).json({
          success: false,
          message: "Email not verified",
          error: {
            type: "AUTHORIZATION_ERROR",
            details:
              "Please verify your email before logging in. We have sent a new verification OTP to your email address.",
          },
        });
        return;
      }

      // Check if user is active
      if (user.status !== UserStatus.ACTIVE) {
        res.status(403).json({
          success: false,
          message: "Account is not active",
          error: {
            type: "AUTHORIZATION_ERROR",
            details: "Your account is pending approval or has been suspended",
          },
        });
        return;
      }

      // Generate JWT token
      const jwtSecret: Secret = process.env.JWT_SECRET as string;
      const jwtOptions: SignOptions = { expiresIn: "1d" };
      const token = jwt.sign(
        { id: user.id, email: user.email, user_type: user.user_type },
        jwtSecret,
        jwtOptions
      );

      // Return user data and token
      res.status(200).json({
        success: true,
        message: "Login successful",
        data: {
          user: {
            id: user.id,
            email: user.email,
            status: user.status,
          },
          token,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { email } = req.body;

      if (!email) {
        res.status(400).json({
          success: false,
          message: "Email is required",
          error: {
            type: "VALIDATION_ERROR",
            details: { email: ["Email is required"] },
          },
        });
        return;
      }

      const userService = ServiceFactory.getUserService();
      await userService.sendPasswordResetEmail(email);

      res.status(200).json({
        success: true,
        message: "Password reset email sent",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user profile
   */
  static async getProfile(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: "Unauthorized",
          error: {
            type: "AUTHENTICATION_ERROR",
            details: "Authentication required",
          },
        });
        return;
      }

      // Get user data
      const userService = ServiceFactory.getUserService();
      const user = await userService.getUserById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          message: "User not found",
          error: {
            type: "NOT_FOUND_ERROR",
            details: "User profile could not be found",
          },
        });
        return;
      }

      // Prepare base user data
      const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        email_verified: user.email_verified,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };

      // Get additional data based on user type
      let additionalData = {};

      if (user.user_type === UserType.ACCESS_OPERATOR) {
        // Get access operator data
        const accessOperator = await userService.getAccessOperatorData(user.id);

        if (accessOperator) {
          additionalData = {
            business_name: accessOperator.business_name,
            address: accessOperator.address,
            geo_latitude: accessOperator.geo_latitude,
            geo_longitude: accessOperator.geo_longitude,
            approved: accessOperator.approved,
          };
        }
      } else if (user.user_type === UserType.CAR_OPERATOR) {
        // Get car operator data
        const carOperator = await userService.getCarOperatorData(user.id);

        if (carOperator) {
          additionalData = {
            license_number: carOperator.license_number,
            vehicle_info: carOperator.vehicle_info,
            approved: carOperator.approved,
            pickup_access_point_id: carOperator.pickup_access_point_id,
            dropoff_access_point_id: carOperator.dropoff_access_point_id,
          };
        }
      }

      // Merge base user data with additional data
      const completeUserData = {
        ...userData,
        ...additionalData,
      };

      // Return complete user data
      res.status(200).json({
        success: true,
        message: "User profile retrieved successfully",
        data: {
          user: completeUserData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate update profile request
   */
  static validateUpdateProfile = validateRequest(UserUpdateSchema);

  /**
   * Update user profile
   */
  static async updateProfile(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: "Unauthorized",
          error: {
            type: "AUTHENTICATION_ERROR",
            details: "Authentication required",
          },
        });
        return;
      }

      // Get basic user data
      const { name, phone } = req.body;

      // Get user service
      const userService = ServiceFactory.getUserService();

      // Get current user to determine user type
      const currentUser = await userService.getUserById(userId);
      if (!currentUser) {
        res.status(404).json({
          success: false,
          message: "User not found",
          error: {
            type: "NOT_FOUND_ERROR",
            details: "User profile could not be found",
          },
        });
        return;
      }

      // Extract user type-specific data based on user type
      let accessOperatorData = null;
      let carOperatorData = null;

      if (currentUser.user_type === UserType.ACCESS_OPERATOR) {
        // Extract access operator data
        const { business_name, address, geo_latitude, geo_longitude } =
          req.body;
        if (
          business_name ||
          address ||
          geo_latitude !== undefined ||
          geo_longitude !== undefined
        ) {
          accessOperatorData = {
            business_name,
            address,
          } as any;

          // Handle geo_latitude - allow null to delete location
          if (geo_latitude !== undefined) {
            accessOperatorData.geo_latitude =
              geo_latitude === null ? null : parseFloat(geo_latitude);
          }

          // Handle geo_longitude - allow null to delete location
          if (geo_longitude !== undefined) {
            accessOperatorData.geo_longitude =
              geo_longitude === null ? null : parseFloat(geo_longitude);
          }
        }
      } else if (currentUser.user_type === UserType.CAR_OPERATOR) {
        // Extract car operator data
        const {
          license_number,
          vehicle_info,
          pickup_access_point_id,
          dropoff_access_point_id,
        } = req.body;
        if (
          license_number ||
          vehicle_info ||
          pickup_access_point_id !== undefined ||
          dropoff_access_point_id !== undefined
        ) {
          carOperatorData = {
            license_number,
            vehicle_info,
            pickup_access_point_id,
            dropoff_access_point_id,
          };
        }
      }

      // Update user data (password updates should use the dedicated changePassword endpoint)
      const updatedUser = await userService.updateUser(
        userId,
        { name, phone },
        accessOperatorData,
        carOperatorData,
        userId // Pass the current user as updatedBy
      );

      // Get complete user data including user type-specific data
      let completeUserData: Record<string, any> = {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone,
        user_type: updatedUser.user_type,
        status: updatedUser.status,
        email_verified: updatedUser.email_verified,
        created_at: updatedUser.created_at,
        updated_at: updatedUser.updated_at,
      };

      // Get additional data based on user type
      if (updatedUser.user_type === UserType.ACCESS_OPERATOR) {
        const accessOperator = await userService.getAccessOperatorData(
          updatedUser.id
        );
        if (accessOperator) {
          completeUserData = {
            ...completeUserData,
            business_name: accessOperator.business_name,
            address: accessOperator.address,
            geo_latitude: accessOperator.geo_latitude,
            geo_longitude: accessOperator.geo_longitude,
            approved: accessOperator.approved,
          };
        }
      } else if (updatedUser.user_type === UserType.CAR_OPERATOR) {
        const carOperator = await userService.getCarOperatorData(
          updatedUser.id
        );
        if (carOperator) {
          completeUserData = {
            ...completeUserData,
            license_number: carOperator.license_number,
            vehicle_info: carOperator.vehicle_info,
            approved: carOperator.approved,
            pickup_access_point_id: carOperator.pickup_access_point_id,
            dropoff_access_point_id: carOperator.dropoff_access_point_id,
          };
        }
      }

      // Return updated user data
      res.status(200).json({
        success: true,
        message: "Profile updated successfully",
        data: {
          user: completeUserData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate OTP verification request
   */
  static validateVerifyOTP = validateRequest(VerifyOTPSchema);

  /**
   * Validate send OTP request
   */
  static validateSendOTP = validateRequest(SendOTPSchema);

  /**
   * Validate resend OTP request
   */
  static validateResendOTP = validateRequest(ResendOTPSchema);

  /**
   * Send verification OTP
   */
  static async sendVerificationOTP(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { email } = req.body;

      const userService = ServiceFactory.getUserService();
      await userService.sendVerificationOTP(email);

      res.status(200).json({
        success: true,
        message: "Verification OTP sent successfully",
        data: {
          email,
          message: "Please check your email for the 6-digit verification code",
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify email with OTP
   */
  static async verifyEmailWithOTP(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { otp } = req.body;

      const userService = ServiceFactory.getUserService();
      const user = await userService.verifyEmailWithOTP(otp);

      res.status(200).json({
        success: true,
        message: "Email verified successfully",
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            user_type: user.user_type,
            status: user.status,
            email_verified: user.email_verified,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Resend verification OTP
   */
  static async resendVerificationOTP(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { email } = req.body;

      const userService = ServiceFactory.getUserService();
      await userService.resendVerificationOTP(email);

      res.status(200).json({
        success: true,
        message: "Verification OTP resent successfully",
        data: {
          email,
          message:
            "Please check your email for the new 6-digit verification code",
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate email verification request
   */
  static validateEmailVerification = validateRequest(
    UserController.EmailVerificationSchema
  );

  /**
   * Validate password reset request
   */
  static validatePasswordReset = validateRequest(
    UserController.PasswordResetSchema
  );

  /**
   * Verify password reset token
   */
  static async verifyPasswordResetToken(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const email = req.query.email as string;
      const token = req.query.token as string;

      if (!email || !token) {
        res.status(400).json({
          success: false,
          message: "Email and token are required",
          error: {
            type: "VALIDATION_ERROR",
            details: {
              email: !email ? ["Email is required"] : [],
              token: !token ? ["Token is required"] : [],
            },
          },
        });
        return;
      }

      // Verify the token
      const userService = ServiceFactory.getUserService();
      await userService.verifyPasswordResetToken(email, token);

      // Return success message
      res.status(200).json({
        success: true,
        message:
          "Token verified successfully. You can now reset your password.",
        data: {
          email,
          token,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          error: {
            type: error.type,
            details: error.details,
          },
        });
      } else {
        next(error);
      }
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { email, token, password } = req.body;

      // Validate inputs
      if (!email || !token || !password) {
        res.status(400).json({
          success: false,
          message: "Email, token, and password are required",
          error: {
            type: "VALIDATION_ERROR",
            details: {
              email: !email ? ["Email is required"] : [],
              token: !token ? ["Token is required"] : [],
              password: !password ? ["Password is required"] : [],
            },
          },
        });
        return;
      }

      // Validate password length
      if (password.length < 8) {
        res.status(400).json({
          success: false,
          message: "Password must be at least 8 characters long",
          error: {
            type: "VALIDATION_ERROR",
            details: {
              password: ["Password must be at least 8 characters long"],
            },
          },
        });
        return;
      }

      // Reset password
      const userService = ServiceFactory.getUserService();
      await userService.resetPassword(email, token, password);

      // Return success message
      res.status(200).json({
        success: true,
        message: "Password reset successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          error: {
            type: error.type,
            details: error.details,
          },
        });
      } else {
        next(error);
      }
    }
  }

  /**
   * Validate change password request
   */
  static validateChangePassword = validateRequest(
    UserController.ChangePasswordSchema
  );

  /**
   * Change password (for authenticated users)
   */
  static async changePassword(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: "Authentication required",
          error: {
            type: "AUTHENTICATION_ERROR",
            details: "User not authenticated",
          },
        });
        return;
      }

      // Change password
      const userService = ServiceFactory.getUserService();
      await userService.changePassword(userId, currentPassword, newPassword);

      // Return success message
      res.status(200).json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          error: {
            type: error.type,
            details: error.details,
          },
        });
      } else {
        next(error);
      }
    }
  }

  /**
   * Get all users (admin only) with pagination, filtering, and searching
   */
  static async getAllUsers(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Parse pagination parameters
      const { paginationOptions, paginationMeta } = parsePaginationParams(
        req.query
      );
      const { page, limit } = paginationMeta;
      const { skip, take } = paginationOptions;

      // Extract filtering and searching parameters
      const search = req.query.search as string | undefined;
      const status = req.query.status as string | undefined;
      const user_type = req.query.user_type as string | undefined;
      const approval_status = req.query.approval_status as string | undefined;

      // Get users with pagination, filtering, and searching
      const userService = ServiceFactory.getUserService();
      const { users, total } = await userService.getAllUsers({
        skip,
        take,
        search,
        status,
        user_type,
        approval_status,
      });

      // Map users to response format with complete user information
      const mappedUsers = await Promise.all(
        users.map(async (user) => {
          // Prepare base user data
          const userData = {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            user_type: user.user_type,
            status: user.status,
            email_verified: user.email_verified,
            created_at: user.created_at,
            updated_at: user.updated_at,
          };

          // Get additional data based on user type and determine approval status
          let additionalData = {};
          let approvalStatus = 'APPROVED'; // Default for customers

          if (user.user_type === UserType.ACCESS_OPERATOR) {
            // Get access operator data
            const accessOperator = await userService.getAccessOperatorData(
              user.id
            );
            if (accessOperator) {
              approvalStatus = accessOperator.approved ? 'APPROVED' : 'REJECTED';
              additionalData = {
                business_name: accessOperator.business_name,
                address: accessOperator.address,
                geo_latitude: accessOperator.geo_latitude,
                geo_longitude: accessOperator.geo_longitude,
                approved: accessOperator.approved,
              };
            } else {
              approvalStatus = 'PENDING';
            }
          } else if (user.user_type === UserType.CAR_OPERATOR) {
            // Get car operator data
            const carOperator = await userService.getCarOperatorData(user.id);
            if (carOperator) {
              approvalStatus = carOperator.approved ? 'APPROVED' : 'REJECTED';
              additionalData = {
                license_number: carOperator.license_number,
                vehicle_info: carOperator.vehicle_info,
                approved: carOperator.approved,
                pickup_access_point_id: carOperator.pickup_access_point_id,
                dropoff_access_point_id: carOperator.dropoff_access_point_id,
              };
            } else {
              approvalStatus = 'PENDING';
            }
          }

          // Merge base user data with additional data and approval status
          return {
            ...userData,
            ...additionalData,
            approval_status: approvalStatus,
          };
        })
      );

      // Return users with consistent pagination metadata
      res.status(200).json({
        success: true,
        message: "Users retrieved successfully",
        data: {
          users: mappedUsers,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: (page + 1) * limit < total,
            hasPrev: page > 0,
          },
        },
      });
    } catch (error) {
      if (
        error instanceof Error &&
        (error.message.includes("Page number") ||
          error.message.includes("Limit must"))
      ) {
        res.status(400).json({
          error: "Invalid pagination parameters",
          message: error.message,
        });
        return;
      }
      next(error);
    }
  }

  /**
   * Change user status (admin only)
   */
  static async changeUserStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Check if user is admin
      const userType = req.user?.user_type;
      if (userType !== UserType.ADMIN) {
        res.status(403).json({
          success: false,
          message: "Forbidden",
          error: {
            type: "AUTHORIZATION_ERROR",
            details: "Only administrators can access this resource",
          },
        });
        return;
      }

      const { id, status } = req.body;

      if (!id || !status) {
        res.status(400).json({
          success: false,
          message: "User ID and status are required",
          error: {
            type: "VALIDATION_ERROR",
            details: {
              id: ["User ID is required"],
              status: ["Status is required"],
            },
          },
        });
        return;
      }

      // Change user status
      const userService = ServiceFactory.getUserService();
      const user = await userService.changeUserStatus(id, status);

      // Prepare base user data
      const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        email_verified: user.email_verified,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };

      // Get additional data based on user type
      let additionalData = {};
      if (user.user_type === UserType.ACCESS_OPERATOR) {
        // Get access operator data
        const accessOperator = await userService.getAccessOperatorData(user.id);
        if (accessOperator) {
          additionalData = {
            business_name: accessOperator.business_name,
            address: accessOperator.address,
            geo_latitude: accessOperator.geo_latitude,
            geo_longitude: accessOperator.geo_longitude,
            approved: accessOperator.approved,
          };
        }
      } else if (user.user_type === UserType.CAR_OPERATOR) {
        // Get car operator data
        const carOperator = await userService.getCarOperatorData(user.id);
        if (carOperator) {
          additionalData = {
            license_number: carOperator.license_number,
            vehicle_info: carOperator.vehicle_info,
            approved: carOperator.approved,
            pickup_access_point_id: carOperator.pickup_access_point_id,
            dropoff_access_point_id: carOperator.dropoff_access_point_id,
          };
        }
      }

      // Merge base user data with additional data
      const completeUserData = {
        ...userData,
        ...additionalData,
      };

      // Return updated user with complete information
      res.status(200).json({
        success: true,
        message: "User status changed successfully",
        data: {
          user: completeUserData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout user
   */
  static async logout(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: "Authentication required",
          error: {
            type: "AUTHENTICATION_ERROR",
            details: "User not authenticated",
          },
        });
        return;
      }

      // Logout user
      const userService = ServiceFactory.getUserService();
      await userService.logout(userId);

      // Return success message
      res.status(200).json({
        success: true,
        message: "Logged out successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Change operator approval status (admin only)
   */
  static async changeOperatorApproval(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id, approved } = req.body;

      if (!id || approved === undefined) {
        res.status(400).json({
          success: false,
          message: "User ID and approval status are required",
          error: {
            type: "VALIDATION_ERROR",
            details: {
              id: id ? [] : ["User ID is required"],
              approved:
                approved !== undefined ? [] : ["Approval status is required"],
            },
          },
        });
        return;
      }

      // Get user to check type
      const userService = ServiceFactory.getUserService();
      const user = await userService.getUserById(id);

      if (!user) {
        res.status(404).json({
          success: false,
          message: "User not found",
          error: {
            type: "NOT_FOUND_ERROR",
            details: "User could not be found",
          },
        });
        return;
      }

      // Check if user is an operator
      if (
        user.user_type !== UserType.ACCESS_OPERATOR &&
        user.user_type !== UserType.CAR_OPERATOR
      ) {
        res.status(400).json({
          success: false,
          message: "Invalid user type",
          error: {
            type: "VALIDATION_ERROR",
            details:
              "Only Access Operators and Car Operators can be approved/disapproved",
          },
        });
        return;
      }

      // Update approval status based on user type
      let updatedOperator;
      if (user.user_type === UserType.ACCESS_OPERATOR) {
        updatedOperator = await userService.updateAccessOperatorApproval(
          id,
          approved
        );
      } else {
        updatedOperator = await userService.updateCarOperatorApproval(
          id,
          approved
        );
      }

      // Prepare complete user data
      const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        email_verified: user.email_verified,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };

      // Add operator-specific data
      let additionalData = {};
      if (user.user_type === UserType.ACCESS_OPERATOR) {
        additionalData = {
          business_name: updatedOperator.business_name,
          address: updatedOperator.address,
          geo_latitude: updatedOperator.geo_latitude,
          geo_longitude: updatedOperator.geo_longitude,
          approved: updatedOperator.approved,
        };
      } else {
        additionalData = {
          license_number: updatedOperator.license_number,
          vehicle_info: updatedOperator.vehicle_info,
          approved: updatedOperator.approved,
        };
      }

      // Merge data
      const completeUserData = {
        ...userData,
        ...additionalData,
      };

      // Return updated operator
      res.status(200).json({
        success: true,
        message: `${user.user_type.toLowerCase().replace("_", " ")} ${
          approved ? "approved" : "disapproved"
        } successfully`,
        data: {
          user: completeUserData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user by ID (admin only) - returns complete user info based on user type
   */
  static async getUserById(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.params.id;
      const userService = ServiceFactory.getUserService();
      const user = await userService.getUserById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          message: "User not found",
          error: {
            type: "NOT_FOUND_ERROR",
            details: "User could not be found",
          },
        });
        return;
      }

      // Prepare base user data
      const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        email_verified: user.email_verified,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };

      // Get additional data based on user type
      let additionalData = {};
      if (user.user_type === UserType.ACCESS_OPERATOR) {
        // Get access operator data
        const accessOperator = await userService.getAccessOperatorData(user.id);
        if (accessOperator) {
          additionalData = {
            business_name: accessOperator.business_name,
            address: accessOperator.address,
            geo_latitude: accessOperator.geo_latitude,
            geo_longitude: accessOperator.geo_longitude,
            approved: accessOperator.approved,
          };
        }
      } else if (user.user_type === UserType.CAR_OPERATOR) {
        // Get car operator data
        const carOperator = await userService.getCarOperatorData(user.id);
        if (carOperator) {
          additionalData = {
            license_number: carOperator.license_number,
            vehicle_info: carOperator.vehicle_info,
            approved: carOperator.approved,
            pickup_access_point_id: carOperator.pickup_access_point_id,
            dropoff_access_point_id: carOperator.dropoff_access_point_id,
          };
        }
      }

      // Merge base user data with additional data
      const completeUserData = {
        ...userData,
        ...additionalData,
      };

      res.status(200).json({
        success: true,
        message: "User retrieved successfully",
        data: {
          user: completeUserData,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Auto-approve all existing operators (development utility)
   * DELETE this endpoint when admin dashboard is ready
   */
  static async autoApproveAllOperators(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userService = ServiceFactory.getUserService();
      const result = await userService.autoApproveAllOperators();

      res.status(200).json({
        success: true,
        message: "All operators have been auto-approved",
        data: {
          accessOperatorsApproved: result.accessOperators,
          carOperatorsApproved: result.carOperators,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get admin actions for the current user
   */
  static async getMyAdminActions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("User not authenticated");
      }

      const userService = ServiceFactory.getUserService();
      const adminActionService = (userService as any).adminActionService;

      const adminActions = await adminActionService.getUserAdminActions(userId);

      res.status(200).json({
        success: true,
        data: {
          adminActions,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
