import { Router } from "express";

import adminRoutes from "./adminRoutes";
import notificationRoutes from "./notificationRoutes";
import { AccessOperatorController } from "../controllers/AccessOperatorController";
import { DashboardController } from "../controllers/DashboardController";
import { PhotoUploadController } from "../controllers/PhotoUploadController";
import { QRLabelController } from "../controllers/QRLabelController";
import { ShipmentController } from "../controllers/ShipmentController";
import { UserController } from "../controllers/UserController";
import { authenticate, authorize } from "../middleware/auth";
import { requireActiveAndApprovedOperator } from "../middleware/operatorStatusMiddleware";
import { uploadPhoto, uploadToS3 } from "../middleware/upload";
import { userContextMiddleware } from "../middleware/userContext";
import { UserType } from "../types/models";

const router = Router();

// Auth routes
router.post(
  "/auth/register",
  UserController.validateRegister,
  UserController.register
);
router.post("/auth/login", UserController.validateLogin, UserController.login);
router.post("/auth/forgot-password", UserController.requestPasswordReset);
// Password reset flow
router.get("/auth/verify-reset-token", UserController.verifyPasswordResetToken);
router.post(
  "/auth/reset-password",
  UserController.validatePasswordReset,
  UserController.resetPassword
);
// Change password (authenticated users)
router.post(
  "/auth/change-password",
  authenticate,
  UserController.validateChangePassword,
  UserController.changePassword
);

// Email verification with OTP
router.post(
  "/auth/send-verification-otp",
  UserController.validateSendOTP,
  UserController.sendVerificationOTP
);
router.post(
  "/auth/verify-email",
  UserController.validateVerifyOTP,
  UserController.verifyEmailWithOTP
);
router.post(
  "/auth/resend-verification-otp",
  UserController.validateResendOTP,
  UserController.resendVerificationOTP
);

// User routes
router.get(
  "/users/profile",
  authenticate,
  userContextMiddleware,
  UserController.getProfile
);
router.put(
  "/users/profile",
  authenticate,
  userContextMiddleware,
  UserController.validateUpdateProfile,
  UserController.updateProfile
);

// Get admin actions for current user
router.get(
  "/users/admin-actions",
  authenticate,
  UserController.getMyAdminActions
);

// Development utility - auto-approve all operators (DELETE when admin dashboard is ready)
router.post(
  "/dev/auto-approve-operators",
  UserController.autoApproveAllOperators
);

// Dashboard routes
router.get("/dashboard", authenticate, DashboardController.getDashboard);
router.get(
  "/dashboard/summary",
  authenticate,
  DashboardController.getDashboardSummary
);

// Admin routes - all admin routes are now in adminRoutes.ts
router.use("/admin", adminRoutes);

// Notification routes
router.use("/notifications", notificationRoutes);

// Shipment routes
router.post(
  "/shipments",
  authenticate,
  userContextMiddleware,
  authorize([UserType.CUSTOMER]),
  ShipmentController.validateCreateShipment,
  ShipmentController.createShipment
);
router.get(
  "/shipments",
  authenticate,
  userContextMiddleware,
  ShipmentController.validateGetMyShipments,
  ShipmentController.getMyShipments
);
router.get(
  "/shipments/my",
  authenticate,
  userContextMiddleware,
  ShipmentController.validateGetMyShipments,
  ShipmentController.getMyShipments
);
router.get(
  "/shipments/pending",
  authenticate,
  userContextMiddleware,
  ShipmentController.getPendingShipments
);
router.get(
  "/shipments/:id",
  authenticate,
  userContextMiddleware,
  ShipmentController.getShipment
);
router.post(
  "/shipments/:id/cancel",
  authenticate,
  userContextMiddleware,
  authorize([UserType.CUSTOMER]),
  ShipmentController.cancelShipment
);
router.post(
  "/shipments/scan",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([
    UserType.ACCESS_OPERATOR,
    UserType.CAR_OPERATOR,
    UserType.CUSTOMER,
  ]),
  ShipmentController.validateScanShipment,
  ShipmentController.scanShipment
);
router.post(
  "/shipments/deliver",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  ShipmentController.validateDeliverShipment,
  ShipmentController.deliverShipment
);

// Photo Upload routes
// Base64 upload (existing)
router.post("/uploads/photo", authenticate, PhotoUploadController.uploadPhoto);

// Multipart form upload (new)
router.post(
  "/uploads/photo/multipart",
  authenticate,
  uploadPhoto,
  uploadToS3,
  PhotoUploadController.uploadPhotoMultipart
);

// Access Points (Access Operators) routes
router.get(
  "/access-points",
  authenticate,
  AccessOperatorController.getAllAccessOperators
);
router.get(
  "/access-points/:id",
  authenticate,
  AccessOperatorController.getAccessOperatorById
);

// QR Label routes for AOs
router.post(
  "/qr-labels/generate-for-shipment",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  QRLabelController.generateQRForShipment
);
router.post(
  "/qr-labels/generate",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  QRLabelController.generateQRLabels
);
router.post(
  "/qr-labels/use-for-shipment",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  QRLabelController.useUnusedQRForShipment
);
router.get(
  "/qr-labels/unused",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  QRLabelController.getUnusedQRLabels
);
router.post(
  "/qr-labels/generate-pdf",
  authenticate,
  userContextMiddleware,
  requireActiveAndApprovedOperator,
  authorize([UserType.ACCESS_OPERATOR]),
  QRLabelController.generateQRLabelsPDF
);

export default router;
