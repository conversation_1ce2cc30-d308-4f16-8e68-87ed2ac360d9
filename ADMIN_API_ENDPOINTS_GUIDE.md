# Admin API Endpoints Guide

This document provides a complete mapping of all admin API endpoints with their corresponding Zod schemas for request validation and response typing.

## 📋 Schema File
All schemas are defined in: `admin-routes-schemas.ts`

## 🔐 Authentication & Profile Management

### POST `/api/admin/login`
- **Request**: `AdminLoginRequest`
- **Response**: `AdminLoginResponse`
- **Description**: Admin login with email and password

### POST `/api/admin/forgot-password`
- **Request**: `AdminForgotPasswordRequest`
- **Response**: `AdminForgotPasswordResponse`
- **Description**: Request password reset email

### POST `/api/admin/reset-password`
- **Request**: `AdminResetPasswordRequest`
- **Response**: `AdminResetPasswordResponse`
- **Description**: Reset password with token

### GET `/api/admin/profile`
- **Request**: None (authenticated route)
- **Response**: `AdminProfileResponse`
- **Description**: Get current admin profile

### PUT `/api/admin/profile`
- **Request**: `AdminUpdateProfileRequest`
- **Response**: `AdminUpdateProfileResponse`
- **Description**: Update admin profile

### POST `/api/admin/change-password`
- **Request**: `AdminChangePasswordRequest`
- **Response**: `AdminChangePasswordResponse`
- **Description**: Change admin password

### POST `/api/admin/refresh-token`
- **Request**: None (authenticated route)
- **Response**: `AdminRefreshTokenResponse`
- **Description**: Refresh expired admin token

### POST `/api/admin/logout`
- **Request**: None
- **Response**: `AdminLogoutResponse`
- **Description**: Logout admin

## 📊 Dashboard & Analytics

### GET `/api/admin/dashboard`
- **Request**: None
- **Response**: `AdminDashboardResponse`
- **Description**: Get admin dashboard data with metrics

### GET `/api/admin/analytics/overview`
- **Request**: None
- **Response**: `SystemOverviewResponse`
- **Description**: Get system overview analytics

### GET `/api/admin/analytics/users`
- **Request**: None
- **Response**: `UserAnalyticsResponse`
- **Description**: Get user analytics data

### GET `/api/admin/analytics/shipments`
- **Request**: None
- **Response**: `ShipmentAnalyticsResponse`
- **Description**: Get shipment analytics data

### GET `/api/admin/analytics/performance`
- **Request**: None
- **Response**: `PerformanceMetricsResponse`
- **Description**: Get performance metrics

### GET `/api/admin/system/health`
- **Request**: None
- **Response**: `SystemHealthResponse`
- **Description**: Get system health status

## 👥 User Management

### GET `/api/admin/users`
- **Request**: `UsersQuery` (query parameters)
- **Response**: `UsersListResponse`
- **Description**: Get paginated list of users with filtering

### PUT `/api/admin/users/management`
- **Request**: `UserManagementRequest`
- **Response**: `UserManagementResponse`
- **Description**: Update user status and approval

### GET `/api/admin/users/:id`
- **Request**: None (ID in URL)
- **Response**: `UserDetailsResponse`
- **Description**: Get detailed user information

### PUT `/api/admin/users/:id`
- **Request**: `UpdateUserRequest`
- **Response**: `UpdateUserResponse`
- **Description**: Update user information

### POST `/api/admin/users/bulk-approve`
- **Request**: `BulkApproveRequest`
- **Response**: `BulkApproveResponse`
- **Description**: Bulk approve operators

### POST `/api/admin/users/bulk-reject`
- **Request**: `BulkRejectRequest`
- **Response**: `BulkRejectResponse`
- **Description**: Bulk reject operators

### GET `/api/admin/users/:id/activity`
- **Request**: None (ID in URL)
- **Response**: `UserActivityResponse`
- **Description**: Get user activity history

### POST `/api/admin/users/:id/notify`
- **Request**: `NotifyUserRequest`
- **Response**: `NotifyUserResponse`
- **Description**: Send notification to user

### PUT `/api/admin/users/:id/activation-approval`
- **Request**: `UserActivationApprovalRequest`
- **Response**: `UserActivationApprovalResponse`
- **Description**: Update user activation and approval status

## 👨‍💼 Admin Management

### GET `/api/admin/admins`
- **Request**: None
- **Response**: `AdminsListResponse`
- **Description**: Get list of all admins

### POST `/api/admin/admins`
- **Request**: `CreateAdminRequest`
- **Response**: `CreateAdminResponse`
- **Description**: Create new admin

### GET `/api/admin/admins/:id`
- **Request**: None (ID in URL)
- **Response**: `AdminDetailsResponse`
- **Description**: Get admin details with audit logs

### PUT `/api/admin/admins/:id`
- **Request**: `UpdateAdminRequest`
- **Response**: `UpdateAdminResponse`
- **Description**: Update admin information

### POST `/api/admin/admins/:id/status`
- **Request**: `ChangeAdminStatusRequest`
- **Response**: `ChangeAdminStatusResponse`
- **Description**: Change admin status (activate/suspend)

### DELETE `/api/admin/admins/:id`
- **Request**: None (ID in URL)
- **Response**: `DeactivateAdminResponse`
- **Description**: Deactivate admin account

## 📦 Shipment Management

### GET `/api/admin/shipments`
- **Request**: `ShipmentsQuery` (query parameters)
- **Response**: `ShipmentsListResponse`
- **Description**: Get paginated list of shipments with filtering

### GET `/api/admin/shipments/:id`
- **Request**: None (ID in URL)
- **Response**: `ShipmentDetailsResponse`
- **Description**: Get detailed shipment information

### PUT `/api/admin/shipments/:id`
- **Request**: `UpdateShipmentRequest`
- **Response**: `UpdateShipmentResponse`
- **Description**: Update shipment information

### POST `/api/admin/shipments/:id/status`
- **Request**: `UpdateShipmentStatusRequest`
- **Response**: `UpdateShipmentStatusResponse`
- **Description**: Update shipment status

### POST `/api/admin/shipments/bulk-cancel`
- **Request**: `BulkCancelShipmentsRequest`
- **Response**: `BulkCancelShipmentsResponse`
- **Description**: Bulk cancel shipments

### GET `/api/admin/shipments/expired`
- **Request**: None
- **Response**: `ExpiredShipmentsResponse`
- **Description**: Get expired shipments

### POST `/api/admin/shipments/:id/reassign`
- **Request**: `ReassignShipmentRequest`
- **Response**: `ReassignShipmentResponse`
- **Description**: Reassign shipment to different car operator

### GET `/api/admin/shipments/expired-stats`
- **Request**: None
- **Response**: `ExpiredShipmentStatsResponse`
- **Description**: Get expired shipments statistics

## ⚙️ System Settings

### GET `/api/admin/settings`
- **Request**: None
- **Response**: `SystemSettingsResponse`
- **Description**: Get system settings

### PUT `/api/admin/settings`
- **Request**: `UpdateSystemSettingsRequest`
- **Response**: `UpdateSystemSettingsResponse`
- **Description**: Update system settings

### POST `/api/admin/settings/reset`
- **Request**: None
- **Response**: `ResetSystemSettingsResponse`
- **Description**: Reset settings to defaults

### GET `/api/admin/settings/history`
- **Request**: None
- **Response**: `SettingsHistoryResponse`
- **Description**: Get settings change history

## 🔔 Notification Management

### GET `/api/admin/notifications`
- **Request**: None
- **Response**: `NotificationsListResponse`
- **Description**: Get all notifications

### POST `/api/admin/notifications/broadcast`
- **Request**: `BroadcastNotificationRequest`
- **Response**: `BroadcastNotificationResponse`
- **Description**: Broadcast notification to users

### GET `/api/admin/notifications/templates`
- **Request**: None
- **Response**: `NotificationTemplatesResponse`
- **Description**: Get notification templates

### POST `/api/admin/notifications/templates`
- **Request**: `CreateNotificationTemplateRequest`
- **Response**: `CreateNotificationTemplateResponse`
- **Description**: Create notification template

### PUT `/api/admin/notifications/templates/:id`
- **Request**: `UpdateNotificationTemplateRequest`
- **Response**: `UpdateNotificationTemplateResponse`
- **Description**: Update notification template

### GET `/api/admin/notifications/stats`
- **Request**: None
- **Response**: `NotificationStatsResponse`
- **Description**: Get notification statistics

## 🔒 Audit & Security

### GET `/api/admin/audit-logs`
- **Request**: `AuditLogsQuery` (query parameters)
- **Response**: `AuditLogsResponse`
- **Description**: Get audit logs with filtering

### GET `/api/admin/security/login-attempts`
- **Request**: None
- **Response**: `LoginAttemptsResponse`
- **Description**: Get admin login attempts

### GET `/api/admin/security/events`
- **Request**: None
- **Response**: `SecurityEventsResponse`
- **Description**: Get security events

### POST `/api/admin/security/lock-user`
- **Request**: `LockUserRequest`
- **Response**: `LockUserResponse`
- **Description**: Lock user account

### POST `/api/admin/security/unlock-user`
- **Request**: `UnlockUserRequest`
- **Response**: `UnlockUserResponse`
- **Description**: Unlock user account

## 📊 Reporting & Export

### GET `/api/admin/reports/users`
- **Request**: `UserReportsQuery` (query parameters)
- **Response**: `UserReportsResponse`
- **Description**: Get user reports

### GET `/api/admin/reports/shipments`
- **Request**: None
- **Response**: `ShipmentReportsResponse`
- **Description**: Get shipment reports

### GET `/api/admin/reports/system`
- **Request**: None
- **Response**: `SystemReportsResponse`
- **Description**: Get system reports

### POST `/api/admin/export/users`
- **Request**: `ExportUsersRequest`
- **Response**: `ExportUsersResponse`
- **Description**: Export users data

### POST `/api/admin/export/shipments`
- **Request**: `ExportShipmentsRequest`
- **Response**: `ExportShipmentsResponse`
- **Description**: Export shipments data

### GET `/api/admin/export/:id/download`
- **Request**: None (ID in URL)
- **Response**: `ExportDownloadResponse`
- **Description**: Download exported data

## 🚀 Usage Examples

```typescript
import {
  AdminLoginRequest,
  AdminLoginResponse,
  UsersQuery,
  UsersListResponse,
  // ... other schemas
} from './admin-routes-schemas';

// Login example
const loginData = AdminLoginRequest.parse({
  email: '<EMAIL>',
  password: 'password123'
});

// API call with validation
const response = await fetch('/api/admin/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(loginData)
});

const result = AdminLoginResponse.parse(await response.json());

// Query users with validation
const usersQuery = UsersQuery.parse({
  page: 0,
  limit: 20,
  user_type: 'ACCESS_OPERATOR',
  status: 'PENDING'
});

const usersResponse = await fetch(`/api/admin/users?${new URLSearchParams(usersQuery)}`);
const usersResult = UsersListResponse.parse(await usersResponse.json());
```

This guide provides complete type safety and validation for all admin API endpoints.
