# Frontend Password Reset Implementation Guide

This guide explains how to implement the **Frontend-First Password Reset Flow** for the NAQALAT shipment platform.

## Backend Changes Made

✅ **Backend is already updated:**
- Reset email links now point to frontend: `http://localhost:8001/reset-password?token=...&email=...`
- Environment variable `FRONTEND_URL=http://localhost:8001` is configured

## Frontend Implementation Required

### **1. Create Forgot Password Page**

**Route:** `/forgot-password`

```jsx
// pages/ForgotPasswordPage.jsx (or .tsx for TypeScript)
import { useState } from 'react';
import { Link } from 'react-router-dom';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:8000/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
      } else {
        setError(data.message || 'Failed to send reset email');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">Check Your Email</h2>
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">
                We've sent a password reset link to <strong>{email}</strong>
              </p>
              <p className="text-green-700 mt-2">
                Click the link in the email to reset your password.
              </p>
              <p className="text-sm text-green-600 mt-2">
                ⏰ The link will expire in 5 minutes.
              </p>
            </div>
            <div className="mt-6">
              <Link 
                to="/login" 
                className="text-blue-600 hover:text-blue-500"
              >
                Back to Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div>
          <h2 className="text-center text-3xl font-bold text-gray-900">
            Forgot Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your email address and we'll send you a reset link.
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter your email address"
            />
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
            >
              {loading ? 'Sending...' : 'Send Reset Link'}
            </button>
          </div>

          <div className="text-center">
            <Link 
              to="/login" 
              className="text-blue-600 hover:text-blue-500 text-sm"
            >
              Back to Login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
```

### **2. Create Reset Password Page**

**Route:** `/reset-password`

```jsx
// pages/ResetPasswordPage.jsx (or .tsx for TypeScript)
import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';

export default function ResetPasswordPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState(null); // null = checking, true = valid, false = invalid
  const [error, setError] = useState('');

  const token = searchParams.get('token');
  const email = searchParams.get('email');

  // Verify token when component mounts
  useEffect(() => {
    if (token && email) {
      verifyToken();
    } else {
      setError('Invalid reset link - missing token or email');
      setTokenValid(false);
    }
  }, [token, email]);

  const verifyToken = async () => {
    try {
      const response = await fetch(
        `http://localhost:8000/api/auth/verify-reset-token?token=${token}&email=${encodeURIComponent(email)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (response.ok) {
        setTokenValid(true);
      } else {
        const data = await response.json();
        setError(data.message || 'Invalid or expired reset token');
        setTokenValid(false);
      }
    } catch (err) {
      setError('Failed to verify reset token. Please check your internet connection.');
      setTokenValid(false);
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    
    // Frontend validation
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:8000/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          token,
          password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Success - show success message and redirect
        alert('Password reset successfully! You can now log in with your new password.');
        navigate('/login');
      } else {
        setError(data.message || 'Failed to reset password');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Loading state while verifying token
  if (tokenValid === null) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <h2 className="mt-4 text-2xl font-bold text-gray-900">
              Verifying Reset Token...
            </h2>
            <p className="mt-2 text-gray-600">
              Please wait while we verify your reset link.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Invalid token state
  if (tokenValid === false) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <h2 className="text-2xl font-bold text-red-900 mb-2">
                Invalid Reset Link
              </h2>
              <p className="text-red-800">{error}</p>
            </div>
            <div className="mt-6 space-y-4">
              <Link
                to="/forgot-password"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                Request New Reset Link
              </Link>
              <Link
                to="/login"
                className="block text-blue-600 hover:text-blue-500 text-sm"
              >
                Back to Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Valid token - show reset form
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div>
          <h2 className="text-center text-3xl font-bold text-gray-900">
            Reset Your Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your new password for <strong>{email}</strong>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleResetPassword}>
          <div className="space-y-4">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                New Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                minLength={8}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter new password (min 8 characters)"
              />
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                minLength={8}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Confirm new password"
              />
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
            >
              {loading ? 'Resetting Password...' : 'Reset Password'}
            </button>
          </div>

          <div className="text-center">
            <Link 
              to="/login" 
              className="text-blue-600 hover:text-blue-500 text-sm"
            >
              Back to Login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
```

### **3. Update Login Page**

Add "Forgot Password" link to your existing login page:

```jsx
// In your LoginPage component, add this link
<div className="text-center mt-4">
  <Link 
    to="/forgot-password" 
    className="text-blue-600 hover:text-blue-500 text-sm"
  >
    Forgot your password?
  </Link>
</div>
```

### **4. Update Router Configuration**

Add the new routes to your router:

```jsx
// App.jsx or Routes.jsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
// ... other imports

function App() {
  return (
    <Router>
      <Routes>
        {/* Existing routes */}
        <Route path="/login" element={<LoginPage />} />
        
        {/* New password reset routes */}
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />
        
        {/* Other routes */}
      </Routes>
    </Router>
  );
}
```

## API Endpoints Reference

### **1. Request Password Reset**
```
POST http://localhost:8000/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

**Error Response (400/404):**
```json
{
  "success": false,
  "message": "User not found",
  "error": {
    "type": "NOT_FOUND_ERROR",
    "details": "User not found"
  }
}
```

### **2. Verify Reset Token**
```
GET http://localhost:8000/api/auth/verify-reset-token?token=abc123&email=<EMAIL>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Token verified successfully. You can now reset your password.",
  "data": {
    "email": "<EMAIL>",
    "token": "abc123"
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Invalid or expired reset token",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Invalid or expired reset token"
  }
}
```

### **3. Reset Password**
```
POST http://localhost:8000/api/auth/reset-password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "token": "abc123",
  "password": "newpassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Invalid or expired reset token",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": {
      "password": ["Password must be at least 8 characters long"]
    }
  }
}
```

## Complete User Flow

1. **User clicks "Forgot Password"** on login page
2. **Frontend shows forgot password form** (`/forgot-password`)
3. **User enters email** and submits
4. **Frontend calls API** `POST /api/auth/forgot-password`
5. **Backend sends email** with link to `http://localhost:8001/reset-password?token=...&email=...`
6. **User receives email** and clicks the link
7. **Frontend opens reset page** (`/reset-password`) and automatically verifies token
8. **If token valid**, frontend shows password reset form
9. **User enters new password** and submits
10. **Frontend calls API** `POST /api/auth/reset-password`
11. **Success** → Frontend redirects to login page

## Security Features

- ⏰ **5-minute token expiration**
- 🔒 **One-time use tokens** (marked as used after reset)
- 📧 **Email verification required**
- 🔐 **Password hashing** with bcrypt
- 📝 **Complete audit trail**
- ✅ **Frontend and backend validation**

## Environment Configuration

**Backend (.env):**
```env
FRONTEND_URL=http://localhost:8001
PORT=8000
```

**Frontend:**
Make sure your frontend runs on port **8001** to match the backend configuration.

## Testing Checklist

- [ ] Test forgot password with valid email
- [ ] Test forgot password with invalid email
- [ ] Test reset link from email opens frontend page
- [ ] Test token verification (valid/invalid/expired)
- [ ] Test password reset with valid token
- [ ] Test password reset with invalid token
- [ ] Test password validation (min 8 characters)
- [ ] Test password confirmation matching
- [ ] Test redirect to login after successful reset
- [ ] Test network error handling

## Styling Notes

The examples above use **Tailwind CSS** classes. If you're using a different CSS framework:

- Replace Tailwind classes with your framework's equivalents
- The key interactive elements are:
  - Form inputs with validation states
  - Loading states for buttons
  - Error message displays
  - Success message displays
  - Loading spinners

## Next Steps

1. Implement the two pages using the code above
2. Add the routes to your router
3. Update your login page with the "Forgot Password" link
4. Test the complete flow
5. Customize the styling to match your app's design

The backend is ready and waiting for your frontend implementation! 🚀