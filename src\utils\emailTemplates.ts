/**
 * Email Templates
 *
 * This file contains all email templates used in the application.
 * Each template is a function that returns HTML content.
 * This makes it easy to update email styles in the future.
 */

/**
 * Base email template with common styling
 * @param content The content to include in the template
 * @returns HTML string
 */
export function baseTemplate(content: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px; background-color: #ffffff;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #333333; margin: 0;">Shipment Relay Platform</h1>
      </div>
      ${content}
      <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #eaeaea; color: #666666; font-size: 12px; text-align: center;">
        <p>© ${new Date().getFullYear()} Shipment Relay Platform. All rights reserved.</p>
        <p>If you have any questions, please contact our support team.</p>
      </div>
    </div>
  `;
}

/**
 * Welcome email template
 * @param name User's name
 * @param userType User's type (CUSTOMER, ACCESS_OPERATOR, CAR_OPERATOR)
 * @returns HTML string
 */
export function welcomeEmailTemplate(name: string, userType: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Welcome to Shipment Relay Platform!</h2>
    <p>Hello ${name},</p>
    <p>Thank you for registering as a ${userType} on our platform.</p>
    <p>We're excited to have you on board and look forward to providing you with a seamless experience.</p>
    <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Email verification template with OTP
 * @param name User's name
 * @param otp One-time password
 * @returns HTML string
 */
export function verificationOTPEmailTemplate(name: string, otp: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Email Verification Required</h2>
    <p>Hello ${name},</p>
    <p>Thank you for registering with the Shipment Relay Platform!</p>
    <p><strong>Important:</strong> You must verify your email address before you can log in to your account.</p>
    <p>Please use the following One-Time Password (OTP) to verify your email address:</p>
    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #4CAF50;">
        ${otp}
      </div>
    </div>
    <p><strong>This OTP will expire in 5 minutes.</strong></p>
    <p>Enter this code in the verification form to complete your email verification.</p>
    <p>After verification, you'll be able to log in and use all features of the platform.</p>
    <p>If you did not request this verification, please ignore this email.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Email verification template (Legacy method)
 * @param name User's name
 * @param verificationLink Link to verify email
 * @returns HTML string
 */
export function verificationEmailTemplate(name: string, verificationLink: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Email Verification Required</h2>
    <p>Hello ${name},</p>
    <p>Thank you for registering with the Shipment Relay Platform!</p>
    <p><strong>Important:</strong> You must verify your email address before you can log in to your account.</p>
    <p>Please click the button below to verify your email address:</p>
    <p style="text-align: center; margin: 30px 0;">
      <a href="${verificationLink}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        Verify Email
      </a>
    </p>
    <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
    <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">${verificationLink}</p>
    <p><strong>This link will expire in 5 minutes.</strong></p>
    <p>After verification, you'll be able to log in and use all features of the platform.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Password reset email template
 * @param name User's name
 * @param resetLink Link to reset password
 * @returns HTML string
 */
export function passwordResetEmailTemplate(name: string, resetLink: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Password Reset</h2>
    <p>Hello ${name},</p>
    <p>We received a request to reset your password. Click the button below to verify your request:</p>
    <p style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        Verify Reset Request
      </a>
    </p>
    <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
    <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">${resetLink}</p>
    <p><strong>This link will expire in 5 minutes.</strong></p>
    <p>After verification, you will be able to set a new password.</p>
    <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Password changed confirmation email template
 * @param name User's name
 * @returns HTML string
 */
export function passwordChangedEmailTemplate(name: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Password Changed Successfully</h2>
    <p>Hello ${name},</p>
    <p>Your password has been successfully changed.</p>
    <p>If you did not make this change, please contact our support team immediately.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Account activated email template
 * @param name User's name
 * @param reason Admin's reason for activation
 * @returns HTML string
 */
export function accountActivatedEmailTemplate(name: string, reason: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Account Activated</h2>
    <p>Hello ${name},</p>
    <p>Your account has been activated. You can now log in and use all features of the Shipment Relay Platform.</p>
    <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
    </div>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Account deactivated email template
 * @param name User's name
 * @param reason Admin's reason for deactivation
 * @returns HTML string
 */
export function accountDeactivatedEmailTemplate(name: string, reason: string): string {
  const content = `
    <h2 style="color: #dc2626; margin-bottom: 20px;">Account Deactivated</h2>
    <p>Hello ${name},</p>
    <p>Your account has been deactivated by an administrator. Your access to platform features has been temporarily suspended.</p>
    <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
    </div>
    <p>Please contact support if you believe this is an error.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Access operator rejected email template
 * @param name User's name
 * @param reason Admin's reason for rejection
 * @returns HTML string
 */
export function accessOperatorRejectedEmailTemplate(name: string, reason: string): string {
  const content = `
    <h2 style="color: #dc2626; margin-bottom: 20px;">Access Operator Application Rejected</h2>
    <p>Hello ${name},</p>
    <p>We regret to inform you that your Access Operator application has been rejected.</p>
    <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
    </div>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Car operator rejected email template
 * @param name User's name
 * @param reason Admin's reason for rejection
 * @returns HTML string
 */
export function carOperatorRejectedEmailTemplate(name: string, reason: string): string {
  const content = `
    <h2 style="color: #dc2626; margin-bottom: 20px;">Car Operator Application Rejected</h2>
    <p>Hello ${name},</p>
    <p>We regret to inform you that your Car Operator application has been rejected.</p>
    <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
    </div>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Shipment created email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param pickupCode Pickup code
 * @returns HTML string
 */
export function shipmentCreatedEmailTemplate(name: string, shipmentId: string, pickupCode: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Shipment Created Successfully</h2>
    <p>Hello ${name},</p>
    <p>Your shipment has been created successfully.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>Pickup Code:</strong> ${pickupCode}</p>
    </div>
    <p>Please keep the pickup code safe as it will be required for the recipient to collect the package.</p>
    <p>You can track your shipment status on our platform.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Shipment status update email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param status New status
 * @param details Optional details
 * @returns HTML string
 */
export function shipmentStatusUpdateEmailTemplate(
  name: string,
  shipmentId: string,
  status: string,
  details?: string
): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Shipment Status Update</h2>
    <p>Hello ${name},</p>
    <p>Your shipment status has been updated.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>New Status:</strong> ${status}</p>
      ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    </div>
    <p>You can track your shipment on our platform for more details.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * New shipment notification for Access Operator template
 * @param businessName Business name
 * @param shipmentId Shipment ID
 * @returns HTML string
 */
export function newShipmentNotificationAOTemplate(businessName: string, shipmentId: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">New Shipment Alert</h2>
    <p>Hello ${businessName},</p>
    <p>A new shipment has been created with your location as the pickup or delivery point.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
    </div>
    <p>Please check your dashboard for more details.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Available shipment notification for Car Operator template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @param originLocation Origin location
 * @param destLocation Destination location
 * @returns HTML string
 */
export function availableShipmentCOTemplate(
  name: string,
  shipmentId: string,
  originLocation: string,
  destLocation: string
): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Available Shipment</h2>
    <p>Hello ${name},</p>
    <p>A new shipment is available for pickup that matches your route:</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>Origin:</strong> ${originLocation}</p>
      <p><strong>Destination:</strong> ${destLocation}</p>
    </div>
    <p>Please check your dashboard to accept this shipment.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Admin welcome email template
 * @param name Admin's name
 * @param role Admin's role
 * @returns HTML string
 */
export function adminWelcomeEmailTemplate(name: string, role: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Welcome to Shipment Relay Platform Admin Portal!</h2>
    <p>Hello ${name},</p>
    <p>Thank you for joining our team as an <strong>${role}</strong>.</p>
    <p>You now have access to the admin portal where you can manage users, shipments, and other platform operations.</p>
    <p>If you have any questions or need assistance, please contact the system administrator.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Admin password reset email template
 * @param name Admin's name
 * @param resetLink Password reset link
 * @returns HTML string
 */
export function adminPasswordResetEmailTemplate(name: string, resetLink: string): string {
  const content = `
    <h2 style="color: #FF9800; margin-bottom: 20px;">Admin Password Reset</h2>
    <p>Hello ${name},</p>
    <p>You have requested to reset your admin account password.</p>
    <p>Please click the button below to reset your password. This link will expire in 5 minutes.</p>
    <div style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Reset Password</a>
    </div>
    <p>If you did not request this password reset, please ignore this email or contact the system administrator.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Admin password changed email template
 * @param name Admin's name
 * @returns HTML string
 */
export function adminPasswordChangedEmailTemplate(name: string): string {
  const content = `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Admin Password Changed</h2>
    <p>Hello ${name},</p>
    <p>Your admin account password has been successfully changed.</p>
    <p>If you did not make this change, please contact the system administrator immediately.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Admin OTP verification email template
 * @param name Admin's name
 * @param otp One-time password
 * @returns HTML string
 */
export function adminOTPEmailTemplate(name: string, otp: string): string {
  const content = `
    <h2 style="color: #FF9800; margin-bottom: 20px;">Admin Account Verification</h2>
    <p>Hello ${name},</p>
    <p>Thank you for registering as an admin on the Shipment Relay Platform.</p>
    <p>To verify your account, please use the following One-Time Password (OTP):</p>
    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 5px;">
        ${otp}
      </div>
    </div>
    <p><strong>This OTP will expire in 5 minutes.</strong></p>
    <p>If you did not request this verification, please ignore this email or contact the system administrator.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}

/**
 * Shipment expired notification email template
 * @param name User's name
 * @param shipmentId Shipment ID
 * @returns HTML string
 */
export function shipmentExpiredEmailTemplate(name: string, shipmentId: string): string {
  const content = `
    <h2 style="color: #FF5722; margin-bottom: 20px;">Shipment Automatically Cancelled</h2>
    <p>Hello ${name},</p>
    <p>We regret to inform you that your shipment has been automatically cancelled due to the 24-hour expiry policy.</p>
    <div style="background-color: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF9800;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>Reason:</strong> 24-hour pickup deadline exceeded</p>
    </div>
    <p><strong>What happened?</strong></p>
    <p>All shipments must be delivered to an Access Operator within 24 hours of creation. Since this deadline was not met, the shipment has been automatically cancelled by our system.</p>
    <p><strong>What can you do?</strong></p>
    <ul>
      <li>Create a new shipment if you still need to send your package</li>
      <li>Ensure you deliver to an Access Operator within 24 hours next time</li>
      <li>Contact support if you have any questions</li>
    </ul>
    <p>We apologize for any inconvenience this may cause.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;

  return baseTemplate(content);
}