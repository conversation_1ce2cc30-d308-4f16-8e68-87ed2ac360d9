import { Request, Response, NextFunction } from "express";
import { z } from "zod";

import { ServiceFactory } from "../services/ServiceFactory";
import { Language, EmailFormat, NotificationType, NotificationPriority } from "../types/models";
import { AppError } from "../utils/errors";

// Validation schemas
const NotificationFiltersSchema = z.object({
  read: z.boolean().optional(),
  type: z.string().optional(),
  priority: z.string().optional(),
  shipmentId: z.string().uuid().optional(),
  fromDate: z.string().datetime().optional(),
  toDate: z.string().datetime().optional(),
  search: z.string().max(255).optional(),
});

const PaginationSchema = z.object({
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

const NotificationPreferencesSchema = z.object({
  email_notifications: z.boolean().optional(),
  sms_notifications: z.boolean().optional(),
  push_notifications: z.boolean().optional(),
  shipment_created: z.boolean().optional(),
  shipment_status_change: z.boolean().optional(),
  qr_assignment: z.boolean().optional(),
  package_ready: z.boolean().optional(),
  delivery_completed: z.boolean().optional(),
  preferred_language: z.nativeEnum(Language).optional(),
  email_format: z.nativeEnum(EmailFormat).optional(),
});

export class NotificationController {
  /**
   * Get notifications for the authenticated user
   * GET /api/notifications
   */
  static async getNotifications(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      // Parse and validate query parameters
      const paginationResult = PaginationSchema.safeParse({
        page: req.query.page ? parseInt(req.query.page as string) : 0,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
      });

      if (!paginationResult.success) {
        throw AppError.badRequest("Invalid pagination parameters");
      }

      const { page, limit } = paginationResult.data;
      const skip = page * limit;

      // Parse filters
      let readFilter: boolean | undefined;
      if (req.query.read === "true") {
        readFilter = true;
      } else if (req.query.read === "false") {
        readFilter = false;
      }

      const filtersResult = NotificationFiltersSchema.safeParse({
        read: readFilter,
        type: req.query.type as string,
        priority: req.query.priority as string,
        shipmentId: req.query.shipmentId as string,
        fromDate: req.query.fromDate as string,
        toDate: req.query.toDate as string,
        search: req.query.search as string,
      });

      if (!filtersResult.success) {
        throw AppError.badRequest("Invalid filter parameters");
      }

      const filters = filtersResult.data;

      // Convert date strings to Date objects and sanitize search
      const processedFilters: any = { ...filters };
      if (filters.fromDate) {
        processedFilters.fromDate = new Date(filters.fromDate);
      }
      if (filters.toDate) {
        processedFilters.toDate = new Date(filters.toDate);
      }
      if (filters.search) {
        // Sanitize search input to prevent injection attacks
        processedFilters.search = filters.search
          .trim()
          .replace(/[<>]/g, '') // Remove HTML tags
          .replace(/['"]/g, '') // Remove quotes that could break queries
          .substring(0, 255); // Limit length
      }

      const notificationService = ServiceFactory.getNotificationService();

      // Get notifications and count
      const [notifications, totalCount, unreadCount] = await Promise.all([
        notificationService.getNotificationsByUserId(userId, processedFilters, {
          skip,
          take: limit,
        }),
        notificationService.getNotificationCount(userId, processedFilters),
        notificationService.getNotificationCount(userId, { read: false }),
      ]);

      res.status(200).json({
        success: true,
        data: {
          notifications,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit),
            hasNext: (page + 1) * limit < totalCount,
            hasPrev: page > 0,
          },
          unreadCount,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get unread notifications count
   * GET /api/notifications/unread-count
   */
  static async getUnreadCount(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      const notificationService = ServiceFactory.getNotificationService();
      const unreadCount = await notificationService.getNotificationCount(
        userId,
        { read: false }
      );

      res.status(200).json({
        success: true,
        data: {
          unreadCount,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark a notification as read
   * PUT /api/notifications/:id/read
   */
  static async markAsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      const { id } = req.params;
      if (!id) {
        throw AppError.badRequest("Notification ID is required");
      }

      const notificationService = ServiceFactory.getNotificationService();

      // First verify the notification belongs to the user
      const notifications = await notificationService.getNotificationsByUserId(
        userId
      );
      const notification = notifications.find((n) => n.id === id);

      if (!notification) {
        throw AppError.notFound("Notification not found");
      }

      const updatedNotification =
        await notificationService.markNotificationAsRead(id);

      res.status(200).json({
        success: true,
        message: "Notification marked as read",
        data: {
          notification: updatedNotification,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark all notifications as read
   * PUT /api/notifications/mark-all-read
   */
  static async markAllAsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      const notificationService = ServiceFactory.getNotificationService();
      await notificationService.markAllNotificationsAsRead(userId);

      res.status(200).json({
        success: true,
        message: "All notifications marked as read",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get notification preferences
   * GET /api/notifications/preferences
   */
  static async getPreferences(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      const notificationService = ServiceFactory.getNotificationService();
      const preferences = await notificationService.getNotificationPreferences(
        userId
      );

      res.status(200).json({
        success: true,
        data: {
          preferences,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update notification preferences
   * PUT /api/notifications/preferences
   */
  static async updatePreferences(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw AppError.unauthorized("Authentication required");
      }

      const preferencesResult = NotificationPreferencesSchema.safeParse(
        req.body
      );
      if (!preferencesResult.success) {
        throw AppError.badRequest("Invalid preferences data");
      }

      const notificationService = ServiceFactory.getNotificationService();
      const updatedPreferences =
        await notificationService.updateNotificationPreferences(
          userId,
          preferencesResult.data
        );

      res.status(200).json({
        success: true,
        message: "Notification preferences updated",
        data: {
          preferences: updatedPreferences,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get notification filter options
   * GET /api/notifications/filter-options
   */
  static async getFilterOptions(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const notificationTypes = Object.values(NotificationType).map((type) => ({
        value: type,
        label: NotificationController.getNotificationTypeLabel(type),
      }));

      const priorities = Object.values(NotificationPriority).map((priority) => ({
        value: priority,
        label: NotificationController.getPriorityLabel(priority),
      }));

      res.status(200).json({
        success: true,
        data: {
          types: notificationTypes,
          priorities,
          readStatus: [
            { value: "true", label: "Read" },
            { value: "false", label: "Unread" },
            { value: "", label: "All" },
          ],
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get human-readable label for notification type
   */
  private static getNotificationTypeLabel(type: NotificationType): string {
    const labels: Record<NotificationType, string> = {
      [NotificationType.SHIPMENT_CREATED]: "Shipment Created",
      [NotificationType.SHIPMENT_ASSIGNED_QR]: "QR Code Assigned",
      [NotificationType.SHIPMENT_DROPPED_OFF]: "Package Dropped Off",
      [NotificationType.SHIPMENT_PICKED_UP]: "Package Picked Up",
      [NotificationType.SHIPMENT_IN_TRANSIT]: "Package In Transit",
      [NotificationType.SHIPMENT_ARRIVED]: "Package Arrived",
      [NotificationType.SHIPMENT_READY_FOR_DELIVERY]: "Ready for Delivery",
      [NotificationType.SHIPMENT_DELIVERED]: "Package Delivered",
      [NotificationType.SHIPMENT_CANCELLED]: "Shipment Cancelled",
      [NotificationType.SHIPMENT_EXPIRED]: "Shipment Expired",
      [NotificationType.SHIPMENT_STATUS_CHANGED]: "Status Updated",
      [NotificationType.QR_CODE_ASSIGNED]: "QR Code Ready",
      [NotificationType.PACKAGE_READY_FOR_PICKUP]: "Package Ready for Pickup",
      [NotificationType.DELIVERY_REMINDER]: "Delivery Reminder",
      [NotificationType.SYSTEM_ALERT]: "System Alert",
      [NotificationType.USER_REGISTERED]: "User Registered",
      [NotificationType.USER_EMAIL_VERIFIED]: "User Email Verified",
      [NotificationType.OPERATOR_NEEDS_APPROVAL]: "Operator Needs Approval",
      [NotificationType.USER_STATUS_CHANGED]: "User Status Changed",
      [NotificationType.SECURITY_ALERT]: "Security Alert",
      [NotificationType.SYSTEM_ERROR]: "System Error",
      [NotificationType.ADMIN_CREATED]: "Admin Created",
      [NotificationType.BULK_OPERATION_COMPLETED]: "Bulk Operation Completed",
      [NotificationType.SYSTEM_MAINTENANCE]: "System Maintenance",
    };
    return labels[type] || type;
  }

  /**
   * Get human-readable label for priority
   */
  private static getPriorityLabel(priority: NotificationPriority): string {
    const labels: Record<NotificationPriority, string> = {
      [NotificationPriority.LOW]: "Low",
      [NotificationPriority.NORMAL]: "Normal",
      [NotificationPriority.HIGH]: "High",
      [NotificationPriority.URGENT]: "Urgent",
    };
    return labels[priority] || priority;
  }
}
