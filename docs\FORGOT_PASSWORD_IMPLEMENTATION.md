# Forgot Password Functionality - Frontend Implementation Guide

## Overview
The forgot password functionality allows users to reset their password by requesting a reset link via email. The process involves two main steps:
1. Requesting a password reset (sends an email with a reset link)
2. Resetting the password using the token from the email

## API Endpoints

### 1. Request Password Reset
**Endpoint:** `POST /api/auth/forgot-password`
**Description:** Sends a password reset email to the user's email address

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

**Notes:**
- The API will return a success message even if the email doesn't exist (for security reasons)
- Users must wait for the email which contains a reset link

### 2. Verify Password Reset Token
**Endpoint:** `GET /api/auth/verify-reset-token`
**Description:** Verifies if a password reset token is valid

**Query Parameters:**
- `email` (string): User's email address
- `token` (string): Reset token from the email

**Response (Success):**
```json
{
  "success": true,
  "message": "Token verified successfully. You can now reset your password.",
  "data": {
    "email": "<EMAIL>",
    "token": "reset-token-from-email"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Invalid or expired token",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Token is invalid or has expired"
  }
}
```

### 3. Reset Password
**Endpoint:** `POST /api/auth/reset-password`
**Description:** Resets the user's password using the token from the email

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "token": "reset-token-from-email",
  "password": "newSecurePassword123"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Invalid or expired token",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Token is invalid or has expired"
  }
}
```

## Implementation Flow

### Step 1: Forgot Password Form
1. Create a form with an email input field
2. On form submission:
   - Send a POST request to `/api/auth/forgot-password` with the email
   - Show a success message to the user (even if the email doesn't exist)

### Step 2: Handle Reset Link
1. When users click the reset link in their email, they will be directed to your reset password page
2. The URL will contain query parameters: `?email=<EMAIL>&token=reset-token`
3. Extract these parameters from the URL

### Step 3: Verify Token (Optional but Recommended)
1. On page load, verify the token is valid by calling `/api/auth/verify-reset-token` with the email and token
2. If invalid, show an error message and redirect to the forgot password page

### Step 4: Reset Password Form
1. Create a form with a password input field
2. On form submission:
   - Send a POST request to `/api/auth/reset-password` with email, token, and new password
   - On success, redirect the user to the login page
   - On error, show appropriate error messages

## Email Templates

The system sends a password reset email with the following content:

### English Version:
- Subject: "Password Reset - Shipment Relay Platform"
- Contains a button "Verify Reset Request" that links to the reset page
- Includes the reset link in plain text for manual copying
- States that the link expires in 5 minutes

### Arabic Version:
- Subject: "إعادة تعيين كلمة المرور - منصة نقل الشحنات"
- Contains a button "التحقق من طلب إعادة التعيين" that links to the reset page
- Includes the reset link in plain text for manual copying
- States that the link expires in 5 minutes

## Error Handling

### Common Error Responses:
1. **Invalid Token**: "Invalid or expired token"
   - Solution: Redirect user to forgot password page to request a new token

2. **Missing Fields**: "Email, token, and password are required"
   - Solution: Ensure all required fields are filled in the form

3. **Password Requirements**: "Password must be at least 8 characters long"
   - Solution: Validate password length before submission

## Best Practices

1. **Security**:
   - Always use HTTPS for password reset endpoints
   - Implement rate limiting to prevent abuse
   - Tokens should expire after 5 minutes
   - Don't reveal if an email exists in the system

2. **User Experience**:
   - Show clear success/error messages
   - Provide a way to resend the email
   - Implement password strength validation
   - Consider adding a "back to login" link

3. **UI/UX Recommendations**:
   - Use a clean, simple form design
   - Provide visual feedback during API calls
   - Use appropriate input types (email, password)
   - Include password visibility toggle
   - Show password requirements

## Sample Implementation (React/Pseudo-code)

```javascript
// Forgot Password Component
const handleForgotPassword = async (email) => {
  try {
    const response = await fetch('/api/auth/forgot-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    });
    
    const data = await response.json();
    // Show success message regardless of email existence
    setMessage('If your email is registered, you will receive a password reset link');
  } catch (error) {
    setError('An error occurred. Please try again.');
  }
};

// Reset Password Component
const handleResetPassword = async (email, token, password) => {
  try {
    const response = await fetch('/api/auth/reset-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, token, password })
    });
    
    const data = await response.json();
    if (data.success) {
      // Redirect to login page
      navigate('/login');
    } else {
      setError(data.message);
    }
  } catch (error) {
    setError('An error occurred. Please try again.');
  }
};
```

## Validation Rules

### Password Requirements:
- Minimum 8 characters
- Should contain a mix of letters, numbers, and special characters (recommended but not enforced by API)

### Token Expiry:
- Tokens expire after 5 minutes
- Users must request a new reset link if the token expires

## Testing

To test the password reset flow:
1. Submit a valid email to the forgot password endpoint
2. Check your email for the reset link
3. Click the link or manually navigate to the reset page with the token
4. Submit a new password
5. Try logging in with the new password

Note: In development, emails may be sent to Ethereal.email if configured, or logged to the console if email sending is disabled.