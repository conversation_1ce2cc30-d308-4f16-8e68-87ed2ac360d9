/**
 * Bilingual Email Templates
 *
 * This file contains bilingual email templates that combine Arabic and English content.
 * Each template function creates emails with both languages based on user preferences.
 */

import {
  modernBilingualTemplate,
  welcomeEmailTemplateArabic,
  verificationOTPEmailTemplateArabic,
  passwordResetEmailTemplateArabic,
  passwordChangedEmailTemplateArabic,
  shipmentCreatedEmailTemplateArabic,
  shipmentStatusUpdateEmailTemplateArabic,
  modernWelcomeArabicContent,
  modernVerificationOTPArabicContent,
  accountActivatedEmailTemplateArabic,
  accountDeactivatedEmailTemplateArabic,
  accessOperatorApprovedEmailTemplateArabic,
  accessOperatorRejectedEmailTemplateArabic,
  carOperatorApprovedEmailTemplateArabic,
  carOperatorRejectedEmailTemplateArabic,
  modernAccountActivatedArabicContent,
  modernAccountDeactivatedArabicContent,
  modernAccessOperatorApprovedArabicContent,
  modernAccessOperator<PERSON>ej<PERSON>ted<PERSON>rabic<PERSON>ontent,
  modernCarOperatorApp<PERSON><PERSON>rab<PERSON><PERSON>ontent,
  modernCarOperatorRejectedArabicContent
} from './arabicEmailTemplates';
import {
  welcomeEmailTemplate,
  verificationOTPEmailTemplate,
  passwordResetEmailTemplate,
  passwordChangedEmailTemplate,
  shipmentCreatedEmailTemplate,
  shipmentStatusUpdateEmailTemplate,
  accountActivatedEmailTemplate,
  accountDeactivatedEmailTemplate,
  accessOperatorRejectedEmailTemplate,
  carOperatorRejectedEmailTemplate
} from './emailTemplates';
import {
  modernWelcomeEnglishContent,
  modernVerificationOTPEnglishContent
} from './modernEmailTemplates';
import { Language, EmailFormat } from '../types/models';

/**
 * Interface for email template options
 */
export interface EmailTemplateOptions {
  preferredLanguage: Language;
  emailFormat: EmailFormat;
}

/**
 * Interface for email template result
 */
export interface EmailTemplateResult {
  subject: string;
  html: string;
}

/**
 * Generate welcome email based on user preferences
 */
export function generateWelcomeEmail(
  name: string, 
  userType: string, 
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Welcome to Shipment Relay Platform';
  const arabicSubject = 'مرحباً بك في منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: welcomeEmailTemplateArabic(name, userType)
      };
    
    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: welcomeEmailTemplate(name, userType)
      };
    
    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getWelcomeEnglishContent(name, userType);
      const arabicContent = getWelcomeArabicContent(name, userType);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate verification OTP email based on user preferences
 */
export function generateVerificationOTPEmail(
  name: string, 
  otp: string, 
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Verify Your Email - Shipment Relay Platform';
  const arabicSubject = 'التحقق من البريد الإلكتروني - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: verificationOTPEmailTemplateArabic(name, otp)
      };
    
    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: verificationOTPEmailTemplate(name, otp)
      };
    
    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getVerificationOTPEnglishContent(name, otp);
      const arabicContent = getVerificationOTPArabicContent(name, otp);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate password reset email based on user preferences
 */
export function generatePasswordResetEmail(
  name: string, 
  resetLink: string, 
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Password Reset - Shipment Relay Platform';
  const arabicSubject = 'إعادة تعيين كلمة المرور - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: passwordResetEmailTemplateArabic(name, resetLink)
      };
    
    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: passwordResetEmailTemplate(name, resetLink)
      };
    
    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getPasswordResetEnglishContent(name, resetLink);
      const arabicContent = getPasswordResetArabicContent(name, resetLink);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate shipment created email based on user preferences
 */
export function generateShipmentCreatedEmail(
  name: string, 
  shipmentId: string, 
  pickupCode: string, 
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Shipment Created - Shipment Relay Platform';
  const arabicSubject = 'تم إنشاء الشحنة - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: shipmentCreatedEmailTemplateArabic(name, shipmentId, pickupCode)
      };
    
    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: shipmentCreatedEmailTemplate(name, shipmentId, pickupCode)
      };
    
    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getShipmentCreatedEnglishContent(name, shipmentId, pickupCode);
      const arabicContent = getShipmentCreatedArabicContent(name, shipmentId, pickupCode);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

// Helper functions to extract content from templates
function getWelcomeEnglishContent(name: string, userType: string): string {
  return modernWelcomeEnglishContent(name, userType);
}

function getWelcomeArabicContent(name: string, userType: string): string {
  return modernWelcomeArabicContent(name, userType);
}

function getVerificationOTPEnglishContent(name: string, otp: string): string {
  return modernVerificationOTPEnglishContent(name, otp);
}

function getVerificationOTPArabicContent(name: string, otp: string): string {
  return modernVerificationOTPArabicContent(name, otp);
}

function getPasswordResetEnglishContent(name: string, resetLink: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Password Reset</h2>
    <p>Hello ${name},</p>
    <p>We received a request to reset your password. Click the button below to verify your request:</p>
    <p style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        Verify Reset Request
      </a>
    </p>
    <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
    <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">${resetLink}</p>
    <p><strong>This link will expire in 5 minutes.</strong></p>
    <p>After verification, you will be able to set a new password.</p>
    <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;
}

function getPasswordResetArabicContent(name: string, resetLink: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">إعادة تعيين كلمة المرور</h2>
    <p>مرحباً ${name}،</p>
    <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بك. انقر على الزر أدناه للتحقق من طلبك:</p>
    <p style="text-align: center; margin: 30px 0;">
      <a href="${resetLink}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        التحقق من طلب إعادة التعيين
      </a>
    </p>
    <p>إذا لم يعمل الزر، يمكنك أيضاً نسخ ولصق الرابط التالي في متصفحك:</p>
    <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">${resetLink}</p>
    <p><strong>ستنتهي صلاحية هذا الرابط خلال 5 دقائق.</strong></p>
    <p>بعد التحقق، ستتمكن من تعيين كلمة مرور جديدة.</p>
    <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني أو التواصل مع الدعم الفني إذا كان لديك مخاوف.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;
}

function getShipmentCreatedEnglishContent(name: string, shipmentId: string, pickupCode: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Shipment Created Successfully</h2>
    <p>Hello ${name},</p>
    <p>Your shipment has been created successfully.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>Pickup Code:</strong> ${pickupCode}</p>
    </div>
    <p>Please keep the pickup code safe as it will be required for the recipient to collect the package.</p>
    <p>You can track your shipment status on our platform.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;
}

function getShipmentCreatedArabicContent(name: string, shipmentId: string, pickupCode: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تم إنشاء الشحنة بنجاح</h2>
    <p>مرحباً ${name}،</p>
    <p>تم إنشاء شحنتك بنجاح.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>رمز الاستلام:</strong> ${pickupCode}</p>
    </div>
    <p>يرجى الاحتفاظ برمز الاستلام بأمان حيث سيكون مطلوباً للمستلم لاستلام الطرد.</p>
    <p>يمكنك تتبع حالة شحنتك على منصتنا.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;
}

/**
 * Generate password changed email based on user preferences
 */
export function generatePasswordChangedEmail(
  name: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Password Changed - Shipment Relay Platform';
  const arabicSubject = 'تم تغيير كلمة المرور - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: passwordChangedEmailTemplateArabic(name)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: passwordChangedEmailTemplate(name)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getPasswordChangedEnglishContent(name);
      const arabicContent = getPasswordChangedArabicContent(name);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate shipment status update email based on user preferences
 */
export function generateShipmentStatusUpdateEmail(
  name: string,
  shipmentId: string,
  status: string,
  details: string | undefined,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = `Shipment Update: ${status} - Shipment Relay Platform`;
  const arabicSubject = `تحديث الشحنة: ${getShipmentStatusInArabic(status)} - منصة نقل الشحنات`;

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: shipmentStatusUpdateEmailTemplateArabic(name, shipmentId, status, details)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: shipmentStatusUpdateEmailTemplate(name, shipmentId, status, details)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getShipmentStatusUpdateEnglishContent(name, shipmentId, status, details);
      const arabicContent = getShipmentStatusUpdateArabicContent(name, shipmentId, status, details);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

function getPasswordChangedEnglishContent(name: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Password Changed Successfully</h2>
    <p>Hello ${name},</p>
    <p>Your password has been successfully changed.</p>
    <p>If you did not make this change, please contact our support team immediately.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;
}

function getPasswordChangedArabicContent(name: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تم تغيير كلمة المرور بنجاح</h2>
    <p>مرحباً ${name}،</p>
    <p>تم تغيير كلمة المرور الخاصة بك بنجاح.</p>
    <p>إذا لم تقم بهذا التغيير، يرجى التواصل مع فريق الدعم الفني فوراً.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;
}

function getShipmentStatusUpdateEnglishContent(name: string, shipmentId: string, status: string, details?: string): string {
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">Shipment Status Update</h2>
    <p>Hello ${name},</p>
    <p>Your shipment status has been updated.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Shipment ID:</strong> ${shipmentId}</p>
      <p><strong>New Status:</strong> ${status}</p>
      ${details ? `<p><strong>Details:</strong> ${details}</p>` : ''}
    </div>
    <p>You can track your shipment on our platform for more details.</p>
    <p>Best regards,<br>The Shipment Relay Team</p>
  `;
}

function getShipmentStatusUpdateArabicContent(name: string, shipmentId: string, status: string, details?: string): string {
  const statusArabic = getShipmentStatusInArabic(status);
  return `
    <h2 style="color: #4CAF50; margin-bottom: 20px;">تحديث حالة الشحنة</h2>
    <p>مرحباً ${name}،</p>
    <p>تم تحديث حالة شحنتك.</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>رقم الشحنة:</strong> ${shipmentId}</p>
      <p><strong>الحالة الجديدة:</strong> ${statusArabic}</p>
      ${details ? `<p><strong>التفاصيل:</strong> ${details}</p>` : ''}
    </div>
    <p>يمكنك تتبع شحنتك على منصتنا لمزيد من التفاصيل.</p>
    <p>مع أطيب التحيات،<br>فريق منصة نقل الشحنات</p>
  `;
}

// Helper function to get shipment status in Arabic
function getShipmentStatusInArabic(status: string): string {
  switch (status) {
    case 'PENDING':
      return 'في الانتظار';
    case 'AWAITING_PICKUP':
      return 'في انتظار الاستلام';
    case 'PICKED_UP_BY_CO':
      return 'تم الاستلام من قبل مشغل المركبة';
    case 'IN_TRANSIT':
      return 'في الطريق';
    case 'ARRIVED_AT_DESTINATION':
      return 'وصل إلى الوجهة';
    case 'READY_FOR_DELIVERY':
      return 'جاهز للتسليم';
    case 'DELIVERED':
      return 'تم التسليم';
    case 'CANCELLED':
      return 'ملغي';
    default:
      return status;
  }
}

/**
 * Generate account activated email based on user preferences
 */
export function generateAccountActivatedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Account Activated - Shipment Relay Platform';
  const arabicSubject = 'تم تفعيل الحساب - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: accountActivatedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: accountActivatedEmailTemplate(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getAccountActivatedEnglishContent(name, reason);
      const arabicContent = modernAccountActivatedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate account deactivated email based on user preferences
 */
export function generateAccountDeactivatedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Account Deactivated - Shipment Relay Platform';
  const arabicSubject = 'تم إلغاء تفعيل الحساب - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: accountDeactivatedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: accountDeactivatedEmailTemplate(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getAccountDeactivatedEnglishContent(name, reason);
      const arabicContent = modernAccountDeactivatedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate access operator approved email based on user preferences
 */
export function generateAccessOperatorApprovedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Access Operator Application Approved - Shipment Relay Platform';
  const arabicSubject = 'تم الموافقة على طلب مشغل نقطة الوصول - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: accessOperatorApprovedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: getAccessOperatorApprovedEnglishTemplate(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getAccessOperatorApprovedEnglishContent(name, reason);
      const arabicContent = modernAccessOperatorApprovedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate access operator rejected email based on user preferences
 */
export function generateAccessOperatorRejectedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Access Operator Application Rejected - Shipment Relay Platform';
  const arabicSubject = 'تم رفض طلب مشغل نقطة الوصول - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: accessOperatorRejectedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: getAccessOperatorRejectedEnglishContent(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getAccessOperatorRejectedEnglishContent(name, reason);
      const arabicContent = modernAccessOperatorRejectedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate car operator approved email based on user preferences
 */
export function generateCarOperatorApprovedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Car Operator Application Approved - Shipment Relay Platform';
  const arabicSubject = 'تم الموافقة على طلب مشغل السيارة - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: carOperatorApprovedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: getCarOperatorApprovedEnglishTemplate(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getCarOperatorApprovedEnglishContent(name, reason);
      const arabicContent = modernCarOperatorApprovedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

/**
 * Generate car operator rejected email based on user preferences
 */
export function generateCarOperatorRejectedEmail(
  name: string,
  reason: string,
  options: EmailTemplateOptions
): EmailTemplateResult {
  const englishSubject = 'Car Operator Application Rejected - Shipment Relay Platform';
  const arabicSubject = 'تم رفض طلب مشغل السيارة - منصة نقل الشحنات';

  switch (options.emailFormat) {
    case EmailFormat.ARABIC_ONLY:
      return {
        subject: arabicSubject,
        html: carOperatorRejectedEmailTemplateArabic(name, reason)
      };

    case EmailFormat.ENGLISH_ONLY:
      return {
        subject: englishSubject,
        html: getCarOperatorRejectedEnglishContent(name, reason)
      };

    case EmailFormat.BILINGUAL:
    default: {
      const englishContent = getCarOperatorRejectedEnglishContent(name, reason);
      const arabicContent = modernCarOperatorRejectedArabicContent(name, reason);

      return {
        subject: `${arabicSubject} | ${englishSubject}`,
        html: modernBilingualTemplate(arabicContent, englishContent)
      };
    }
  }
}

// Helper functions for English content
function getAccountActivatedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        🎉 Account Activated!
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Great news! Your account has been activated by an administrator.
      </p>
    </div>

    <div style="background-color: #d1fae5; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #065f46; font-size: 16px; font-weight: 600;">✅ Account Status: ACTIVE</h4>
      <p style="margin-bottom: 0; color: #065f46; line-height: 1.5;">
        You now have full access to all platform features. You can create shipments, track packages, and manage your account.
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        Thank you for using NAQALAT, NAQALAT Team
      </p>
    </div>
  `;
}

function getAccessOperatorApprovedEnglishTemplate(name: string, reason: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">🎉 Access Operator Application Approved!</h2>
      <p>Dear <strong>${name}</strong>,</p>
      <p>Congratulations! Your Access Operator application has been approved by our team.</p>

      <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #065f46;">✅ Operator Status: APPROVED</h4>
        <p style="margin-bottom: 0;">You can now receive and manage shipments at your location. Customers can select your access point for their shipments.</p>
      </div>

      <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
        <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br><strong>Naqalat Platform</strong></p>
    </div>
  `;
}

function getAccessOperatorApprovedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        🎉 Access Operator Application Approved!
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Congratulations! Your Access Operator application has been approved by our team.
      </p>
    </div>

    <div style="background-color: #d1fae5; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #065f46; font-size: 16px; font-weight: 600;">✅ Operator Status: APPROVED</h4>
      <p style="margin-bottom: 0; color: #065f46; line-height: 1.5;">
        You can now receive and manage shipments at your location. Customers can select your access point for their shipments.
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        Thank you for using NAQALAT, NAQALAT Team
      </p>
    </div>
  `;
}

function getCarOperatorApprovedEnglishTemplate(name: string, reason: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">🎉 Car Operator Application Approved!</h2>
      <p>Dear <strong>${name}</strong>,</p>
      <p>Congratulations! Your Car Operator application has been approved by our team.</p>

      <div style="background: #d1fae5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #065f46;">✅ Operator Status: APPROVED</h4>
        <p style="margin-bottom: 0;">You can now accept and transport shipments between access points. Start earning by helping customers get their packages delivered!</p>
      </div>

      <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #856404;">📝 Administrator's Note:</h4>
        <p style="margin-bottom: 0; color: #856404; font-style: italic;">"${reason}"</p>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br><strong>Naqalat Platform</strong></p>
    </div>
  `;
}

function getCarOperatorApprovedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        🎉 Car Operator Application Approved!
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Congratulations! Your Car Operator application has been approved by our team.
      </p>
    </div>

    <div style="background-color: #d1fae5; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #065f46; font-size: 16px; font-weight: 600;">✅ Operator Status: APPROVED</h4>
      <p style="margin-bottom: 0; color: #065f46; line-height: 1.5;">
        You can now accept and transport shipments between access points. Start earning by helping customers get their packages delivered!
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        Thank you for using NAQALAT, NAQALAT Team
      </p>
    </div>
  `;
}

// Additional helper functions for new templates
function getAccountDeactivatedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        ⚠️ Account Deactivated
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        Your account has been deactivated by an administrator.
      </p>
    </div>

    <div style="background-color: #fee2e2; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #991b1b; font-size: 16px; font-weight: 600;">❌ Account Status: DEACTIVATED</h4>
      <p style="margin-bottom: 0; color: #991b1b; line-height: 1.5;">
        Your access to platform features has been temporarily suspended. Please contact support if you believe this is an error.
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        NAQALAT Team
      </p>
    </div>
  `;
}

function getAccessOperatorRejectedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        ❌ Application Rejected
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        We regret to inform you that your Access Operator application has been rejected.
      </p>
    </div>

    <div style="background-color: #fee2e2; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #991b1b; font-size: 16px; font-weight: 600;">❌ Application Status: REJECTED</h4>
      <p style="margin-bottom: 0; color: #991b1b; line-height: 1.5;">
        Your application to become an Access Operator has not been approved at this time.
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        NAQALAT Team
      </p>
    </div>
  `;
}

function getCarOperatorRejectedEnglishContent(name: string, reason: string): string {
  return `
    <div style="margin-bottom: 30px; text-align: center;">
      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">
        ❌ Application Rejected
      </h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0;">
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
        Dear <strong>${name}</strong>,
      </h3>
      <p style="margin: 10px 0; line-height: 1.6; color: #333;">
        We regret to inform you that your Car Operator application has been rejected.
      </p>
    </div>

    <div style="background-color: #fee2e2; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #991b1b; font-size: 16px; font-weight: 600;">❌ Application Status: REJECTED</h4>
      <p style="margin-bottom: 0; color: #991b1b; line-height: 1.5;">
        Your application to become a Car Operator has not been approved at this time.
      </p>
    </div>

    <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
      <h4 style="margin-top: 0; color: #856404; font-size: 16px; font-weight: 600;">📝 Administrator's Note:</h4>
      <p style="margin-bottom: 0; color: #856404; line-height: 1.5; font-style: italic;">"${reason}"</p>
    </div>

    <div style="text-align: center; margin: 25px 0;">
      <p style="margin: 5px 0; color: #666; font-size: 14px;">
        NAQALAT Team
      </p>
    </div>
  `;
}
